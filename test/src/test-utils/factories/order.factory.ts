import { OrderItemDto } from '../../../../src/modules/orders/dto/order-item.dto';
import { OrderDto } from '../../../../src/modules/orders/dto/order.dto';
import { generateNewObjectId } from '../../../../src/modules/shared/database/helpers/generate-objectId';
import { OrderStatus } from '../../../../src/modules/orders/models/order-status';
import { OrderConfirmedDto } from '../../../../src/modules/orders/dto/order-confirmed.dto';
import { PaymentInformation } from '../../../../src/modules/orders/models/order-payment-information';
import { Order } from '../../../../src/modules/orders/schema/order.schema';

export class OrderFactory {
  static createOrderItemDto(overrides: Partial<OrderItemDto> = {}): OrderItemDto {
    return {
      name: 'Misto Quente',
      description: 'Delicioso misto quente com queijo e presunto',
      quantity: 1,
      unitPrice: 15.5,
      ...overrides,
    };
  }

  static createOrderDto(overrides: Partial<OrderDto> = {}): OrderDto {
    const orderDto = new OrderDto();
    orderDto.id = generateNewObjectId().toString();
    orderDto.document = '12345678900';
    orderDto.items = [this.createOrderItemDto()];
    orderDto.total = 15.5;
    orderDto.status = OrderStatus.RECEIVED;
    orderDto.orderNumber = Math.floor(Math.random() * 1000) + 1;

    return {
      ...orderDto,
      ...overrides,
    };
  }

  static createOrderConfirmedDto(overrides: Partial<OrderConfirmedDto> = {}): OrderConfirmedDto {
    return {
      orderId: generateNewObjectId().toString(),
      paymentProof: 'proof-123456',
      confirmed: true,
      totalPaid: 15.5,
      ...overrides,
    };
  }

  static createPaymentInformation(overrides: Partial<PaymentInformation> = {}): PaymentInformation {
    const paymentInfo = new PaymentInformation('https://qr-code-url.com', 'https://payment-url.com');
    paymentInfo.paymentProof = 'proof-123456';
    paymentInfo.totalPaid = 15.5;
    paymentInfo.confirmed = true;
    paymentInfo.updateDate = new Date();

    return {
      ...paymentInfo,
      ...overrides,
    };
  }

  static createOrder(overrides: Partial<Order> = {}): Order {
    const order = new Order();
    order._id = generateNewObjectId().toString();
    order.document = '12345678900';
    order.orderNumber = Math.floor(Math.random() * 1000) + 1;
    order.createDate = new Date();
    order.updateDate = new Date();
    order.status = OrderStatus.RECEIVED;
    order.items = [
      {
        name: 'Misto Quente',
        description: 'Delicioso misto quente',
        quantity: 1,
        unitPrice: 15.5,
      } as any,
    ];
    order.paymentInformation = this.createPaymentInformation();
    order.total = 15.5;

    Object.assign(order, overrides);

    return order;
  }

  static createMultipleOrderItems(count: number = 3): OrderItemDto[] {
    return Array.from({ length: count }, (_, index) =>
      this.createOrderItemDto({
        name: `Item ${index + 1}`,
        description: `Descrição do item ${index + 1}`,
        unitPrice: (index + 1) * 10,
      }),
    );
  }

  static createOrderWithMultipleItems(itemCount: number = 3): OrderDto {
    const items = this.createMultipleOrderItems(itemCount);
    const total = items.reduce((sum, item) => sum + item.quantity * item.unitPrice, 0);

    return this.createOrderDto({
      items,
      total,
    });
  }
}
