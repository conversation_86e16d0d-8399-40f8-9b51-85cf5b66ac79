import { RedisClientType } from 'redis';

export const createMockRedisClient = (): jest.Mocked<RedisClientType> =>
  ({
    connect: jest.fn(),
    disconnect: jest.fn(),
    quit: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    ttl: jest.fn(),
    keys: jest.fn(),
    flushAll: jest.fn(),
    flushDb: jest.fn(),
    ping: jest.fn(),
    info: jest.fn(),
    isOpen: true,
    isReady: true,
  }) as any;

export const mockRedisConnectionService = {
  getClient: jest.fn(),
  clientIsConnected: jest.fn(),
};
