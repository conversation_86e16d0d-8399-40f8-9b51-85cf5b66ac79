import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { MercadoPagoServicePortToken } from '../../../../src/modules/mercadopago/constants/mercado-pago.constants';
import { OrderManagementServicePortToken, OrderRepositoryPortToken } from '../../../../src/modules/orders/constants/order.constants';
import { RedisConnectionService } from '../../../../src/modules/shared/redis/redis-connection.service';
import { mockMercadoPagoService } from '../mocks/mercadopago.mock';
import { createMockModel } from '../mocks/mongoose.mock';
import { mockRedisConnectionService } from '../mocks/redis.mock';

export class TestHelpers {
  /**
   * Cria um módulo de teste com providers mockados comuns
   */
  static async createTestingModule(providers: any[] = [], imports: any[] = []): Promise<TestingModule> {
    return Test.createTestingModule({
      imports,
      providers,
    }).compile();
  }

  /**
   * Cria mocks para o modelo do Mongoose
   */
  static createMongooseMocks(modelNames: string[]) {
    return modelNames.map((name) => ({
      provide: getModelToken(name),
      useValue: createMockModel(),
    }));
  }

  /**
   * Cria providers mockados comuns para testes
   */
  static createCommonMockProviders() {
    return [
      {
        provide: RedisConnectionService,
        useValue: mockRedisConnectionService,
      },
      {
        provide: MercadoPagoServicePortToken,
        useValue: mockMercadoPagoService,
      },
    ];
  }

  /**
   * Cria providers mockados para o módulo Orders
   */
  static createOrdersMockProviders() {
    return [
      ...this.createMongooseMocks(['Order']),
      ...this.createCommonMockProviders(),
      {
        provide: OrderRepositoryPortToken,
        useValue: {
          create: jest.fn(),
          confirmOrder: jest.fn(),
        },
      },
      {
        provide: OrderManagementServicePortToken,
        useValue: {
          issueOrder: jest.fn(),
          confirmOrder: jest.fn(),
        },
      },
    ];
  }

  /**
   * Limpa todos os mocks
   */
  static clearAllMocks() {
    jest.clearAllMocks();
  }

  /**
   * Reseta todos os mocks
   */
  static resetAllMocks() {
    jest.resetAllMocks();
  }

  /**
   * Cria um spy para console.error para testes de logging
   */
  static spyOnConsoleError() {
    return jest.spyOn(console, 'error').mockImplementation(() => {});
  }

  /**
   * Cria um spy para console.log para testes de logging
   */
  static spyOnConsoleLog() {
    return jest.spyOn(console, 'log').mockImplementation(() => {});
  }

  /**
   * Aguarda um tempo específico (útil para testes assíncronos)
   */
  static async wait(ms: number = 100): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Cria um mock para process.env
   */
  static mockProcessEnv(envVars: Record<string, string>) {
    const originalEnv = process.env;
    process.env = { ...originalEnv, ...envVars };

    return () => {
      process.env = originalEnv;
    };
  }

  /**
   * Verifica se uma função foi chamada com argumentos específicos
   */
  static expectCalledWith(mockFn: jest.Mock, ...args: any[]) {
    expect(mockFn).toHaveBeenCalledWith(...args);
  }

  /**
   * Verifica se uma função foi chamada um número específico de vezes
   */
  static expectCalledTimes(mockFn: jest.Mock, times: number) {
    expect(mockFn).toHaveBeenCalledTimes(times);
  }
}
