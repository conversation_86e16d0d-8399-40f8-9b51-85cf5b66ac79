import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { MercadoPagoServicePortToken } from '../../../../../../src/modules/mercadopago/constants/mercado-pago.constants';
import { PaymentRequestDto } from '../../../../../../src/modules/mercadopago/dto/payment-request.dto';
import { MercadoPagoServicePort } from '../../../../../../src/modules/mercadopago/services/mercado-pago.service.port';
import { OrderRepositoryPortToken } from '../../../../../../src/modules/orders/constants/order.constants';
import { OrderRepositoryPort } from '../../../../../../src/modules/orders/control/repositories/order.repository.port';
import { OrderRegistrationService } from '../../../../../../src/modules/orders/control/services/order-registration.service';
import { OrderStatus } from '../../../../../../src/modules/orders/models/order-status';
import { VitolaException } from '../../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { MercadoPagoFactory } from '../../../../test-utils/factories/mercadopago.factory';
import { OrderFactory } from '../../../../test-utils/factories/order.factory';
import { TestHelpers } from '../../../../test-utils/helpers/test.helpers';

describe('OrderRegistrationService', () => {
  let service: OrderRegistrationService;
  let orderRepository: jest.Mocked<OrderRepositoryPort>;
  let mercadoPagoService: jest.Mocked<MercadoPagoServicePort>;
  let loggerSpy: jest.SpyInstance;

  beforeEach(async () => {
    const mockOrderRepository = {
      create: jest.fn(),
      confirmOrder: jest.fn(),
    };

    const mockMercadoPagoService = {
      createPayment: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrderRegistrationService,
        {
          provide: OrderRepositoryPortToken,
          useValue: mockOrderRepository,
        },
        {
          provide: MercadoPagoServicePortToken,
          useValue: mockMercadoPagoService,
        },
      ],
    }).compile();

    service = module.get<OrderRegistrationService>(OrderRegistrationService);
    orderRepository = module.get(OrderRepositoryPortToken);
    mercadoPagoService = module.get(MercadoPagoServicePortToken);

    loggerSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('issueOrder', () => {
    it('should create an order successfully', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const paymentResponse = MercadoPagoFactory.createPaymentResponseDto();
      const createdOrderId = 'created-order-id';

      mercadoPagoService.createPayment.mockResolvedValue(paymentResponse);
      orderRepository.create.mockResolvedValue(createdOrderId);

      const result = await service.issueOrder(orderDto);

      expect(result).toBeDefined();
      expect(result.id).toBe(createdOrderId);
      expect(result.paymentInformation).toBeDefined();
      expect(result.paymentInformation.paymentUrl).toBe(paymentResponse.paymentUrl);
      expect(result.paymentInformation.qrCodeUrl).toBe(paymentResponse.qrCode);
      expect(result.status).toBe(OrderStatus.RECEIVED);
      expect(result.orderNumber).toBeGreaterThan(0);
      expect(result.total).toBeGreaterThan(0);

      expect(mercadoPagoService.createPayment).toHaveBeenCalledWith(expect.any(PaymentRequestDto));
      expect(orderRepository.create).toHaveBeenCalledWith(orderDto);
    });

    it('should throw error when order is null', async () => {
      const nullOrder = null as any;

      await expect(service.issueOrder(nullOrder)).rejects.toBeVitolaException('ORDER_NOT_FOUND', 'Escolha ao menos 1 item para criar o pedido.');

      expect(mercadoPagoService.createPayment).not.toHaveBeenCalled();
      expect(orderRepository.create).not.toHaveBeenCalled();
    });

    it('should throw error when order is undefined', async () => {
      const undefinedOrder = undefined as any;

      await expect(service.issueOrder(undefinedOrder)).rejects.toBeVitolaException('ORDER_NOT_FOUND', 'Escolha ao menos 1 item para criar o pedido.');
    });

    it('should handle MercadoPago service error', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const mercadoPagoError = VitolaException.ofError('PAYMENT_DATA_ERROR', 'Erro ao criar pagamento');

      mercadoPagoService.createPayment.mockRejectedValue(mercadoPagoError);

      await expect(service.issueOrder(orderDto)).rejects.toThrow(mercadoPagoError);

      expect(mercadoPagoService.createPayment).toHaveBeenCalled();
      expect(orderRepository.create).not.toHaveBeenCalled();
    });

    it('should handle repository creation error', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const paymentResponse = MercadoPagoFactory.createPaymentResponseDto();
      const repositoryError = new Error('Database connection error');

      mercadoPagoService.createPayment.mockResolvedValue(paymentResponse);
      orderRepository.create.mockRejectedValue(repositoryError);

      await expect(service.issueOrder(orderDto)).rejects.toBeVitolaException('ORDER_CREATION_ERROR', 'Ocorreu um erro ao criar pedido.');

      expect(mercadoPagoService.createPayment).toHaveBeenCalled();
      expect(orderRepository.create).toHaveBeenCalled();
      expect(loggerSpy).toHaveBeenCalledWith(expect.stringContaining('Erro durante criação do pedido:'));
    });

    it('should validate order before processing', async () => {
      const invalidOrder = OrderFactory.createOrderDto({ items: [] });

      await expect(service.issueOrder(invalidOrder)).rejects.toBeVitolaException('ITEMS_NOT_FOUND', 'Items do pedido são obrigatórios.');

      expect(mercadoPagoService.createPayment).not.toHaveBeenCalled();
      expect(orderRepository.create).not.toHaveBeenCalled();
    });

    it('should create payment request with correct data', async () => {
      const orderDto = OrderFactory.createOrderDto({
        total: 15.5,
        orderNumber: 123,
      });
      const paymentResponse = MercadoPagoFactory.createPaymentResponseDto();

      mercadoPagoService.createPayment.mockResolvedValue(paymentResponse);
      orderRepository.create.mockResolvedValue('created-order-id');

      service.issueOrder(orderDto);

      expect(mercadoPagoService.createPayment).toHaveBeenCalledWith(
        expect.objectContaining({
          id: orderDto.id,
          title: `Pedido: ${orderDto.orderNumber}`,
          unit_price: 15.5,
          quantity: 1,
        }),
      );
    });

    it('should set payment information correctly', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const paymentResponse = MercadoPagoFactory.createPaymentResponseDto({
        paymentUrl: 'https://custom-payment-url.com',
        qrCode: 'https://custom-qr-code.com',
      });

      mercadoPagoService.createPayment.mockResolvedValue(paymentResponse);
      orderRepository.create.mockResolvedValue('created-order-id');

      const result = await service.issueOrder(orderDto);

      expect(result.paymentInformation).toEqual(
        expect.objectContaining({
          paymentUrl: 'https://custom-payment-url.com',
          qrCodeUrl: 'https://custom-qr-code.com',
          confirmed: false,
          createDate: expect.any(Date),
        }),
      );
    });
  });

  describe('confirmOrder', () => {
    it('should confirm an order successfully', async () => {
      const orderId = 'valid-order-id';
      const orderConfirmed = OrderFactory.createOrderConfirmedDto();

      orderRepository.confirmOrder.mockResolvedValue(true);

      const result = await service.confirmOrder(orderId, orderConfirmed);

      expect(result).toBe(true);
      expect(orderRepository.confirmOrder).toHaveBeenCalledWith(orderId, orderConfirmed);
    });

    it('should throw error when orderId is null', async () => {
      const nullOrderId = null as any;
      const orderConfirmed = OrderFactory.createOrderConfirmedDto();

      await expect(service.confirmOrder(nullOrderId, orderConfirmed)).rejects.toBeVitolaException('ORDERID_INVALID', 'Identificação do pedido inválida');

      expect(orderRepository.confirmOrder).not.toHaveBeenCalled();
    });

    it('should throw error when orderId is empty string', async () => {
      const emptyOrderId = '';
      const orderConfirmed = OrderFactory.createOrderConfirmedDto();

      await expect(service.confirmOrder(emptyOrderId, orderConfirmed)).rejects.toBeVitolaException('ORDERID_INVALID', 'Identificação do pedido inválida');
    });

    it('should throw error when orderConfirmed is null', async () => {
      const orderId = 'valid-order-id';
      const nullOrderConfirmed = null as any;

      await expect(service.confirmOrder(orderId, nullOrderConfirmed)).rejects.toBeVitolaException('ORDER_INVALID', 'Pedido inválida');

      expect(orderRepository.confirmOrder).not.toHaveBeenCalled();
    });

    it('should throw error when orderConfirmed is undefined', async () => {
      const orderId = 'valid-order-id';
      const undefinedOrderConfirmed = undefined as any;

      await expect(service.confirmOrder(orderId, undefinedOrderConfirmed)).rejects.toBeVitolaException('ORDER_INVALID', 'Pedido inválida');
    });

    it('should handle repository error', async () => {
      const orderId = 'valid-order-id';
      const orderConfirmed = OrderFactory.createOrderConfirmedDto();
      const repositoryError = new Error('Database error');

      orderRepository.confirmOrder.mockRejectedValue(repositoryError);

      await expect(service.confirmOrder(orderId, orderConfirmed)).rejects.toThrow(repositoryError);

      expect(orderRepository.confirmOrder).toHaveBeenCalledWith(orderId, orderConfirmed);
    });

    it('should return false when order is not found', async () => {
      const orderId = 'non-existent-order-id';
      const orderConfirmed = OrderFactory.createOrderConfirmedDto();

      orderRepository.confirmOrder.mockResolvedValue(false);

      const result = await service.confirmOrder(orderId, orderConfirmed);

      expect(result).toBe(false);
      expect(orderRepository.confirmOrder).toHaveBeenCalledWith(orderId, orderConfirmed);
    });
  });

  describe('Service Integration', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should have dependencies injected', () => {
      expect(orderRepository).toBeDefined();
      expect(mercadoPagoService).toBeDefined();
    });
  });
});
