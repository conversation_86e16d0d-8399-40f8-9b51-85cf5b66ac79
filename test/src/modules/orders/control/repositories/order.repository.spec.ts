import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { Model } from 'mongoose';
import { OrderRepository } from '../../../../../../src/modules/orders/control/repositories/order.repository';
import { PaymentInformation } from '../../../../../../src/modules/orders/models/order-payment-information';
import { OrderStatus } from '../../../../../../src/modules/orders/models/order-status';
import { generateNewObjectId, objectIdFromStringValue } from '../../../../../../src/modules/shared/database/helpers/generate-objectId';
import { OrderFactory } from '../../../../test-utils/factories/order.factory';
import { TestHelpers } from '../../../../test-utils/helpers/test.helpers';
import { createMockModel, mockUpdateResult, mockUpdateResultNotFound } from '../../../../test-utils/mocks/mongoose.mock';

// Interface para representar Order sem dependências do Mongoose
interface IOrder {
  _id: string;
  document?: string;
  orderNumber: number;
  createDate: Date;
  updateDate: Date;
  status: OrderStatus;
  items: any[];
  paymentInformation?: PaymentInformation;
  total: number;
}

describe('OrderRepository', () => {
  let repository: OrderRepository;
  let orderModel: jest.Mocked<Model<IOrder>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrderRepository,
        {
          provide: getModelToken('Order'),
          useValue: createMockModel<IOrder>(),
        },
      ],
    }).compile();

    repository = module.get<OrderRepository>(OrderRepository);
    orderModel = module.get(getModelToken('Order'));
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('create', () => {
    it('should create an order successfully', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const createdOrder: IOrder = {
        _id: generateNewObjectId().toString(),
        document: orderDto.document,
        orderNumber: orderDto.orderNumber,
        status: orderDto.status,
        createDate: new Date(),
        updateDate: new Date(),
        items: orderDto.items.map(
          (item) =>
            ({
              name: item.name,
              description: item.description,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
            }) as any,
        ),
        paymentInformation: orderDto.paymentInformation,
        total: orderDto.total,
      };

      orderModel.create.mockResolvedValue(createdOrder as any);

      const result = await repository.create(orderDto);

      expect(result).toBe(createdOrder._id);
      expect(orderModel.create).toHaveBeenCalledWith({
        _id: expect.any(String),
        document: orderDto.document,
        orderNumber: orderDto.orderNumber,
        status: orderDto.status,
        items: orderDto.items,
        paymentInformation: orderDto.paymentInformation,
        total: orderDto.total,
        createDate: expect.any(Date),
        updateDate: expect.any(Date),
      });
    });

    it('should handle creation error', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const creationError = new Error('Database connection error');

      orderModel.create.mockRejectedValue(creationError);

      await expect(repository.create(orderDto)).rejects.toThrow(creationError);
      expect(orderModel.create).toHaveBeenCalled();
    });

    it('should create order with correct ObjectId', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const createdOrder: IOrder = {
        _id: generateNewObjectId().toString(),
        document: orderDto.document,
        orderNumber: orderDto.orderNumber,
        status: orderDto.status,
        createDate: new Date(),
        updateDate: new Date(),
        items: orderDto.items,
        paymentInformation: orderDto.paymentInformation,
        total: orderDto.total,
      };

      orderModel.create.mockResolvedValue(createdOrder as any);

      const result = await repository.create(orderDto);

      expect(result).toHaveValidObjectId();
      expect(orderModel.create).toHaveBeenCalledWith(
        expect.objectContaining({
          _id: expect.any(String),
        }),
      );
    });

    it('should create order with all required fields', async () => {
      const orderDto = OrderFactory.createOrderDto({
        document: '12345678900',
        items: OrderFactory.createMultipleOrderItems(2),
        total: 50.75,
      });
      const createdOrder: IOrder = {
        _id: generateNewObjectId().toString(),
        document: orderDto.document,
        orderNumber: orderDto.orderNumber,
        status: orderDto.status,
        createDate: new Date(),
        updateDate: new Date(),
        items: orderDto.items,
        paymentInformation: orderDto.paymentInformation,
        total: orderDto.total,
      };

      orderModel.create.mockResolvedValue(createdOrder as any);

      await repository.create(orderDto);

      expect(orderModel.create).toHaveBeenCalledWith(
        expect.objectContaining({
          document: '12345678900',
          items: expect.arrayContaining([
            expect.objectContaining({
              name: expect.any(String),
              description: expect.any(String),
              quantity: expect.any(Number),
              unitPrice: expect.any(Number),
            }),
          ]),
          total: 50.75,
          status: expect.any(String),
          orderNumber: expect.any(Number),
          createDate: expect.any(Date),
          updateDate: expect.any(Date),
        }),
      );
    });

    it('should create order without document', async () => {
      const orderDto = OrderFactory.createOrderDto({ document: undefined });
      const createdOrder: IOrder = {
        _id: generateNewObjectId().toString(),
        document: orderDto.document,
        orderNumber: orderDto.orderNumber,
        status: orderDto.status,
        createDate: new Date(),
        updateDate: new Date(),
        items: orderDto.items,
        paymentInformation: orderDto.paymentInformation,
        total: orderDto.total,
      };

      orderModel.create.mockResolvedValue(createdOrder as any);

      await repository.create(orderDto);

      expect(orderModel.create).toHaveBeenCalledWith(
        expect.objectContaining({
          document: undefined,
        }),
      );
    });
  });

  describe('confirmOrder', () => {
    it('should confirm an order successfully', async () => {
      const orderId = generateNewObjectId().toString();
      const orderConfirmed = OrderFactory.createOrderConfirmedDto({
        paymentProof: 'payment-proof-123',
        totalPaid: 25.5,
        confirmed: true,
      });

      orderModel.updateOne.mockResolvedValue(mockUpdateResult as any);

      const result = await repository.confirmOrder(orderId, orderConfirmed);

      expect(result).toBe(true);
      expect(orderModel.updateOne).toHaveBeenCalledWith(
        {
          _id: objectIdFromStringValue(orderId),
        },
        {
          $set: {
            status: OrderStatus.PREPARING,
            updateDate: expect.any(Date),
            paymentInformation: {
              paymentProof: 'payment-proof-123',
              totalPaid: 25.5,
              confirmed: true,
              updateDate: expect.any(Date),
            },
          },
        },
      );
    });

    it('should return false when order is not found', async () => {
      const orderId = generateNewObjectId().toString();
      const orderConfirmed = OrderFactory.createOrderConfirmedDto();

      orderModel.updateOne.mockResolvedValue(mockUpdateResultNotFound as any);

      const result = await repository.confirmOrder(orderId, orderConfirmed);

      expect(result).toBe(false);
      expect(orderModel.updateOne).toHaveBeenCalledWith(
        {
          _id: objectIdFromStringValue(orderId),
        },
        expect.any(Object),
      );
    });

    it('should handle update error', async () => {
      const orderId = generateNewObjectId().toString();
      const orderConfirmed = OrderFactory.createOrderConfirmedDto();
      const updateError = new Error('Database update error');

      orderModel.updateOne.mockRejectedValue(updateError);

      await expect(repository.confirmOrder(orderId, orderConfirmed)).rejects.toThrow(updateError);
      expect(orderModel.updateOne).toHaveBeenCalled();
    });

    it('should update order status to PREPARING', async () => {
      const orderId = generateNewObjectId().toString();
      const orderConfirmed = OrderFactory.createOrderConfirmedDto();

      orderModel.updateOne.mockResolvedValue(mockUpdateResult as any);

      await repository.confirmOrder(orderId, orderConfirmed);

      expect(orderModel.updateOne).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          $set: expect.objectContaining({
            status: OrderStatus.PREPARING,
          }),
        }),
      );
    });

    it('should update payment information correctly', async () => {
      const orderId = generateNewObjectId().toString();
      const orderConfirmed = OrderFactory.createOrderConfirmedDto({
        paymentProof: 'custom-proof',
        totalPaid: 99.99,
        confirmed: true,
      });

      orderModel.updateOne.mockResolvedValue(mockUpdateResult as any);

      await repository.confirmOrder(orderId, orderConfirmed);

      expect(orderModel.updateOne).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          $set: expect.objectContaining({
            paymentInformation: {
              paymentProof: 'custom-proof',
              totalPaid: 99.99,
              confirmed: true,
              updateDate: expect.any(Date),
            },
          }),
        }),
      );
    });

    it('should set updateDate when confirming order', async () => {
      const orderId = generateNewObjectId().toString();
      const orderConfirmed = OrderFactory.createOrderConfirmedDto();

      orderModel.updateOne.mockResolvedValue(mockUpdateResult as any);

      await repository.confirmOrder(orderId, orderConfirmed);

      expect(orderModel.updateOne).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          $set: expect.objectContaining({
            updateDate: expect.any(Date),
            paymentInformation: expect.objectContaining({
              updateDate: expect.any(Date),
            }),
          }),
        }),
      );
    });

    it('should handle partial update result', async () => {
      const orderId = generateNewObjectId().toString();
      const orderConfirmed = OrderFactory.createOrderConfirmedDto();
      const partialUpdateResult = {
        acknowledged: true,
        matchedCount: 1,
        modifiedCount: 0, // No modifications made
        upsertedCount: 0,
        upsertedId: null,
      };

      orderModel.updateOne.mockResolvedValue(partialUpdateResult as any);

      const result = await repository.confirmOrder(orderId, orderConfirmed);

      expect(result).toBe(false);
    });
  });

  describe('Repository Integration', () => {
    it('should be defined', () => {
      expect(repository).toBeDefined();
    });

    it('should have orderModel injected', () => {
      expect(orderModel).toBeDefined();
    });

    it('should handle concurrent operations', async () => {
      const id1 = generateNewObjectId().toString();
      const id2 = generateNewObjectId().toString();
      const orderDto1 = OrderFactory.createOrderDto();
      const orderDto2 = OrderFactory.createOrderDto();
      const createdOrder1: IOrder = {
        _id: id1,
        document: orderDto1.document,
        orderNumber: orderDto1.orderNumber,
        status: orderDto1.status,
        createDate: new Date(),
        updateDate: new Date(),
        items: orderDto1.items,
        paymentInformation: orderDto1.paymentInformation,
        total: orderDto1.total,
      };

      const createdOrder2: IOrder = {
        _id: id2,
        document: orderDto2.document,
        orderNumber: orderDto2.orderNumber,
        status: orderDto2.status,
        createDate: new Date(),
        updateDate: new Date(),
        items: orderDto2.items,
        paymentInformation: orderDto2.paymentInformation,
        total: orderDto2.total,
      };

      orderModel.create.mockResolvedValueOnce(createdOrder1 as any).mockResolvedValueOnce(createdOrder2 as any);

      const [result1, result2] = await Promise.all([repository.create(orderDto1), repository.create(orderDto2)]);

      expect(result1).toBe(id1);
      expect(result2).toBe(id2);
      expect(orderModel.create).toHaveBeenCalledTimes(2);
    });
  });
});
