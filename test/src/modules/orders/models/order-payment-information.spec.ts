import { PaymentInformation } from '../../../../../src/modules/orders/models/order-payment-information';
import { PaymentMethod } from '../../../../../src/modules/orders/models/order-payment-method';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { MercadoPagoFactory } from '../../../test-utils/factories/mercadopago.factory';
import { OrderFactory } from '../../../test-utils/factories/order.factory';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('PaymentInformation', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create payment information with required fields', () => {
      const paymentInfo = new PaymentInformation('https://qr-code-url.com', 'https://payment-url.com');

      expect(paymentInfo.qrCodeUrl).toBe('https://qr-code-url.com');
      expect(paymentInfo.paymentUrl).toBe('https://payment-url.com');
      expect(paymentInfo.paymentMethod).toBe(PaymentMethod.QRCODEPIX);
      expect(paymentInfo.confirmed).toBe(false);
      expect(paymentInfo.createDate).toBeValidDate();
    });

    it('should set default values correctly', () => {
      const paymentInfo = new PaymentInformation('qr-url', 'payment-url');

      expect(paymentInfo.paymentMethod).toBe(PaymentMethod.QRCODEPIX);
      expect(paymentInfo.confirmed).toBe(false);
      expect(paymentInfo.createDate).toBeInstanceOf(Date);
      expect(paymentInfo.paymentProof).toBeUndefined();
      expect(paymentInfo.totalPaid).toBeUndefined();
      expect(paymentInfo.updateDate).toBeUndefined();
    });
  });

  describe('of static method', () => {
    it('should create payment information from PaymentResponseDto', () => {
      const paymentResponse = MercadoPagoFactory.createPaymentResponseDto({
        qrCode: 'https://custom-qr-code.com',
        paymentUrl: 'https://custom-payment-url.com',
      });

      const paymentInfo = PaymentInformation.of(paymentResponse);

      expect(paymentInfo).toBeInstanceOf(PaymentInformation);
      expect(paymentInfo.qrCodeUrl).toBe('https://custom-qr-code.com');
      expect(paymentInfo.paymentUrl).toBe('https://custom-payment-url.com');
      expect(paymentInfo.paymentMethod).toBe(PaymentMethod.QRCODEPIX);
      expect(paymentInfo.confirmed).toBe(false);
    });

    it('should handle empty PaymentResponseDto', () => {
      const emptyPaymentResponse = MercadoPagoFactory.createPaymentResponseDto({
        qrCode: '',
        paymentUrl: '',
      });

      const paymentInfo = PaymentInformation.of(emptyPaymentResponse);

      expect(paymentInfo.qrCodeUrl).toBe('');
      expect(paymentInfo.paymentUrl).toBe('');
    });
  });

  describe('validate static method', () => {
    it('should validate payment information successfully', () => {
      const paymentInfo = OrderFactory.createPaymentInformation({
        paymentMethod: PaymentMethod.QRCODEPIX,
        qrCodeUrl: 'https://qr-code.com',
        paymentProof: 'proof-123',
        totalPaid: 25.5,
      });

      expect(() => PaymentInformation.validate(paymentInfo)).not.toThrow();
    });

    describe('paymentMethod validation', () => {
      it('should throw error when paymentMethod is null', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentMethod: null as any,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow(VitolaException);
        expect(() => PaymentInformation.validate(paymentInfo)).toThrow('Forma de pagamento é obrigatória.');
      });

      it('should throw error when paymentMethod is undefined', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentMethod: undefined as any,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow(VitolaException);
      });

      it('should accept valid paymentMethod', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentMethod: PaymentMethod.QRCODEPIX,
          qrCodeUrl: 'https://qr-code.com',
          paymentProof: 'proof-123',
          totalPaid: 25.5,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).not.toThrow();
      });
    });

    describe('qrCodeUrl validation for QRCODEPIX', () => {
      it('should throw error when qrCodeUrl is null for QRCODEPIX', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentMethod: PaymentMethod.QRCODEPIX,
          qrCodeUrl: null as any,
          paymentProof: 'proof-123',
          totalPaid: 25.5,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow(VitolaException);
        expect(() => PaymentInformation.validate(paymentInfo)).toThrow('QRCode do pagamento é obrigatório.');
      });

      it('should throw error when qrCodeUrl is undefined for QRCODEPIX', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentMethod: PaymentMethod.QRCODEPIX,
          qrCodeUrl: undefined as any,
          paymentProof: 'proof-123',
          totalPaid: 25.5,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow(VitolaException);
      });

      it('should throw error when qrCodeUrl is empty for QRCODEPIX', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentMethod: PaymentMethod.QRCODEPIX,
          qrCodeUrl: '',
          paymentProof: 'proof-123',
          totalPaid: 25.5,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow(VitolaException);
      });

      it('should accept valid qrCodeUrl for QRCODEPIX', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentMethod: PaymentMethod.QRCODEPIX,
          qrCodeUrl: 'https://valid-qr-code.com',
          paymentProof: 'proof-123',
          totalPaid: 25.5,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).not.toThrow();
      });
    });

    describe('paymentProof validation', () => {
      it('should throw error when paymentProof is null', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentProof: null as any,
          totalPaid: 25.5,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow(VitolaException);
        expect(() => PaymentInformation.validate(paymentInfo)).toThrow('Comprovante de pagamento é obrigatória.');
      });

      it('should throw error when paymentProof is undefined', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentProof: undefined as any,
          totalPaid: 25.5,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow(VitolaException);
      });

      it('should throw error when paymentProof is empty', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentProof: '',
          totalPaid: 25.5,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow(VitolaException);
      });

      it('should accept valid paymentProof', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentProof: 'valid-proof-123',
          totalPaid: 25.5,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).not.toThrow();
      });
    });

    describe('totalPaid validation', () => {
      it('should throw error when totalPaid is null', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentProof: 'proof-123',
          totalPaid: null as any,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow(VitolaException);
        expect(() => PaymentInformation.validate(paymentInfo)).toThrow('Valor do pagamento é obrigatória.');
      });

      it('should throw error when totalPaid is undefined', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentProof: 'proof-123',
          totalPaid: undefined as any,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow(VitolaException);
      });

      it('should throw error when totalPaid is zero', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentProof: 'proof-123',
          totalPaid: 0,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow(VitolaException);
      });

      it('should accept positive totalPaid', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentProof: 'proof-123',
          totalPaid: 25.5,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).not.toThrow();
      });
    });

    describe('combined validation scenarios', () => {
      it('should validate complete payment information', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentMethod: PaymentMethod.QRCODEPIX,
          qrCodeUrl: 'https://qr-code.com',
          paymentProof: 'proof-abc123',
          totalPaid: 99.99,
        });

        expect(() => PaymentInformation.validate(paymentInfo)).not.toThrow();
      });

      it('should throw first validation error encountered', () => {
        const paymentInfo = OrderFactory.createPaymentInformation({
          paymentMethod: null as any, // Invalid
          qrCodeUrl: '', // Invalid
          paymentProof: '', // Invalid
          totalPaid: 0, // Invalid
        });

        expect(() => PaymentInformation.validate(paymentInfo)).toThrow('Forma de pagamento é obrigatória.');
      });
    });
  });

  describe('PaymentInformation Properties', () => {
    it('should allow setting all properties', () => {
      const paymentInfo = new PaymentInformation('qr-url', 'payment-url');

      paymentInfo.paymentProof = 'test-proof';
      paymentInfo.totalPaid = 50.25;
      paymentInfo.confirmed = true;
      paymentInfo.updateDate = new Date();

      expect(paymentInfo.paymentProof).toBe('test-proof');
      expect(paymentInfo.totalPaid).toBe(50.25);
      expect(paymentInfo.confirmed).toBe(true);
      expect(paymentInfo.updateDate).toBeInstanceOf(Date);
    });
  });
});
