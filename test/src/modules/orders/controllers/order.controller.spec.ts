import { Test, TestingModule } from '@nestjs/testing';
import { OrderManagementServicePortToken } from '../../../../../src/modules/orders/constants/order.constants';
import { OrderManagementServicePort } from '../../../../../src/modules/orders/control/services/order-registration.service.port';
import { OrderController } from '../../../../../src/modules/orders/controllers/order.controller';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderFactory } from '../../../test-utils/factories/order.factory';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('OrderController', () => {
  let controller: OrderController;
  let orderService: jest.Mocked<OrderManagementServicePort>;

  beforeEach(async () => {
    const mockOrderService = {
      issueOrder: jest.fn(),
      confirmOrder: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrderController],
      providers: [
        {
          provide: OrderManagementServicePortToken,
          useValue: mockOrderService,
        },
      ],
    }).compile();

    controller = module.get<OrderController>(OrderController);
    orderService = module.get(OrderManagementServicePortToken);
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('issueOrder', () => {
    it('should create an order successfully', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const expectedResult = OrderFactory.createOrderDto({
        id: 'created-order-id',
        paymentInformation: OrderFactory.createPaymentInformation(),
      });

      orderService.issueOrder.mockResolvedValue(expectedResult);

      const result = await controller.issueOrder(orderDto);

      expect(result).toEqual(expectedResult);
      expect(orderService.issueOrder).toHaveBeenCalledWith(orderDto);
      expect(orderService.issueOrder).toHaveBeenCalledTimes(1);
    });

    it('should handle service errors when creating order', async () => {
      const orderDto = OrderFactory.createOrderDto();
      const serviceError = VitolaException.ofValidation('ORDER_CREATION_ERROR', 'Erro ao criar pedido');

      orderService.issueOrder.mockRejectedValue(serviceError);

      await expect(controller.issueOrder(orderDto)).rejects.toThrow(serviceError);
      expect(orderService.issueOrder).toHaveBeenCalledWith(orderDto);
      expect(orderService.issueOrder).toHaveBeenCalledTimes(1);
    });

    it('should handle validation errors from service', async () => {
      const invalidOrderDto = OrderFactory.createOrderDto({ items: [] });
      const validationError = VitolaException.ofValidation('ITEMS_NOT_FOUND', 'Items do pedido são obrigatórios.');

      orderService.issueOrder.mockRejectedValue(validationError);

      await expect(controller.issueOrder(invalidOrderDto)).rejects.toThrow(validationError);
      expect(orderService.issueOrder).toHaveBeenCalledWith(invalidOrderDto);
    });

    it('should handle order with multiple items', async () => {
      const orderWithMultipleItems = OrderFactory.createOrderWithMultipleItems(3);
      const expectedResult = OrderFactory.createOrderDto({
        ...orderWithMultipleItems,
        id: 'created-order-id',
        paymentInformation: OrderFactory.createPaymentInformation(),
      });

      orderService.issueOrder.mockResolvedValue(expectedResult);

      const result = await controller.issueOrder(orderWithMultipleItems);

      expect(result).toEqual(expectedResult);
      expect(result.items).toHaveLength(3);
      expect(orderService.issueOrder).toHaveBeenCalledWith(orderWithMultipleItems);
    });

    it('should handle order without document', async () => {
      const orderWithoutDocument = OrderFactory.createOrderDto({ document: undefined });
      const expectedResult = OrderFactory.createOrderDto({
        ...orderWithoutDocument,
        id: 'created-order-id',
        paymentInformation: OrderFactory.createPaymentInformation(),
      });

      orderService.issueOrder.mockResolvedValue(expectedResult);

      const result = await controller.issueOrder(orderWithoutDocument);

      expect(result).toEqual(expectedResult);
      expect(result.document).toBeUndefined();
      expect(orderService.issueOrder).toHaveBeenCalledWith(orderWithoutDocument);
    });
  });

  describe('confirmOrder', () => {
    it('should confirm an order successfully', async () => {
      const orderId = 'valid-order-id';
      const orderConfirmedDto = OrderFactory.createOrderConfirmedDto();

      orderService.confirmOrder.mockResolvedValue(true);

      const result = await controller.confirmeOrder(orderId, orderConfirmedDto);

      expect(result).toBe(true);
      expect(orderService.confirmOrder).toHaveBeenCalledWith(orderId, orderConfirmedDto);
      expect(orderService.confirmOrder).toHaveBeenCalledTimes(1);
    });

    it('should handle invalid order id', async () => {
      const invalidOrderId = '';
      const orderConfirmedDto = OrderFactory.createOrderConfirmedDto();
      const validationError = VitolaException.ofValidation('ORDERID_INVALID', 'Identificação do pedido inválida');

      orderService.confirmOrder.mockRejectedValue(validationError);

      await expect(controller.confirmeOrder(invalidOrderId, orderConfirmedDto)).rejects.toThrow(validationError);
      expect(orderService.confirmOrder).toHaveBeenCalledWith(invalidOrderId, orderConfirmedDto);
    });

    it('should handle invalid order confirmation data', async () => {
      const orderId = 'valid-order-id';
      const invalidOrderConfirmed = null as any;
      const validationError = VitolaException.ofValidation('ORDER_INVALID', 'Pedido inválida');

      orderService.confirmOrder.mockRejectedValue(validationError);

      await expect(controller.confirmeOrder(orderId, invalidOrderConfirmed)).rejects.toThrow(validationError);
      expect(orderService.confirmOrder).toHaveBeenCalledWith(orderId, invalidOrderConfirmed);
    });

    it('should handle order not found', async () => {
      const nonExistentOrderId = 'non-existent-order-id';
      const orderConfirmedDto = OrderFactory.createOrderConfirmedDto();

      orderService.confirmOrder.mockResolvedValue(false);

      const result = await controller.confirmeOrder(nonExistentOrderId, orderConfirmedDto);

      expect(result).toBe(false);
      expect(orderService.confirmOrder).toHaveBeenCalledWith(nonExistentOrderId, orderConfirmedDto);
    });

    it('should handle service errors when confirming order', async () => {
      const orderId = 'valid-order-id';
      const orderConfirmedDto = OrderFactory.createOrderConfirmedDto();
      const serviceError = new Error('Database connection error');

      orderService.confirmOrder.mockRejectedValue(serviceError);

      await expect(controller.confirmeOrder(orderId, orderConfirmedDto)).rejects.toThrow(serviceError);
      expect(orderService.confirmOrder).toHaveBeenCalledWith(orderId, orderConfirmedDto);
    });

    it('should confirm order with valid payment proof', async () => {
      const orderId = 'valid-order-id';
      const orderConfirmedDto = OrderFactory.createOrderConfirmedDto({
        paymentProof: 'valid-payment-proof-123',
        totalPaid: 25.75,
        confirmed: true,
      });

      orderService.confirmOrder.mockResolvedValue(true);

      const result = await controller.confirmeOrder(orderId, orderConfirmedDto);

      expect(result).toBe(true);
      expect(orderService.confirmOrder).toHaveBeenCalledWith(orderId, orderConfirmedDto);
      expect(orderConfirmedDto.paymentProof).toBe('valid-payment-proof-123');
      expect(orderConfirmedDto.totalPaid).toBe(25.75);
      expect(orderConfirmedDto.confirmed).toBe(true);
    });
  });

  describe('Controller Integration', () => {
    it('should be defined', () => {
      expect(controller).toBeDefined();
    });

    it('should have orderService injected', () => {
      expect(orderService).toBeDefined();
    });

    it('should handle concurrent requests', async () => {
      const orderDto1 = OrderFactory.createOrderDto();
      const orderDto2 = OrderFactory.createOrderDto();
      const expectedResult1 = OrderFactory.createOrderDto({ id: 'order-1' });
      const expectedResult2 = OrderFactory.createOrderDto({ id: 'order-2' });

      orderService.issueOrder.mockResolvedValueOnce(expectedResult1).mockResolvedValueOnce(expectedResult2);

      const [result1, result2] = await Promise.all([controller.issueOrder(orderDto1), controller.issueOrder(orderDto2)]);

      expect(result1).toEqual(expectedResult1);
      expect(result2).toEqual(expectedResult2);
      expect(orderService.issueOrder).toHaveBeenCalledTimes(2);
    });
  });
});
