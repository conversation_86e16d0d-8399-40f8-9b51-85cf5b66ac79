import { OrderItemDto } from '../../../../../src/modules/orders/dto/order-item.dto';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderFactory } from '../../../test-utils/factories/order.factory';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('OrderItemDto', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('validate', () => {
    it('should validate item successfully', () => {
      const item = OrderFactory.createOrderItemDto({
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        description: 'Delicioso hambúrguer artesanal',
        quantity: 2,
        unitPrice: 25.5,
      });

      expect(() => OrderItemDto.validate(item)).not.toThrow();
    });

    describe('name validation', () => {
      it('should throw error when name is null', () => {
        const item = OrderFactory.createOrderItemDto({ name: null as any });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
        expect(() => OrderItemDto.validate(item)).toThrow('Nome do produto é obrigatório.');
      });

      it('should throw error when name is undefined', () => {
        const item = OrderFactory.createOrderItemDto({ name: undefined as any });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
      });

      it('should throw error when name is empty string', () => {
        const item = OrderFactory.createOrderItemDto({ name: '' });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
      });

      it('should accept valid name', () => {
        const item = OrderFactory.createOrderItemDto({ name: 'Pizza Margherita' });

        expect(() => OrderItemDto.validate(item)).not.toThrow();
      });
    });

    describe('description validation', () => {
      it('should throw error when description is null', () => {
        const item = OrderFactory.createOrderItemDto({ description: null as any });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
        expect(() => OrderItemDto.validate(item)).toThrow('Descrição do produto é obrigatório.');
      });

      it('should throw error when description is undefined', () => {
        const item = OrderFactory.createOrderItemDto({ description: undefined as any });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
      });

      it('should throw error when description is empty string', () => {
        const item = OrderFactory.createOrderItemDto({ description: '' });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
      });

      it('should accept valid description', () => {
        const item = OrderFactory.createOrderItemDto({
          description: 'Pizza com molho de tomate, mussarela e manjericão',
        });

        expect(() => OrderItemDto.validate(item)).not.toThrow();
      });
    });

    describe('quantity validation', () => {
      it('should throw error when quantity is null', () => {
        const item = OrderFactory.createOrderItemDto({ quantity: null as any });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
        expect(() => OrderItemDto.validate(item)).toThrow('Quantidade do produto é obrigatório.');
      });

      it('should throw error when quantity is undefined', () => {
        const item = OrderFactory.createOrderItemDto({ quantity: undefined as any });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
      });

      it('should throw error when quantity is zero', () => {
        const item = OrderFactory.createOrderItemDto({ quantity: 0 });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
      });

      it('should throw error when quantity is negative', () => {
        const item = OrderFactory.createOrderItemDto({ quantity: -1 });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
      });

      it('should accept positive quantity', () => {
        const item = OrderFactory.createOrderItemDto({ quantity: 5 });

        expect(() => OrderItemDto.validate(item)).not.toThrow();
      });

      it('should accept decimal quantity', () => {
        const item = OrderFactory.createOrderItemDto({ quantity: 2.5 });

        expect(() => OrderItemDto.validate(item)).not.toThrow();
      });
    });

    describe('unitPrice validation', () => {
      it('should throw error when unitPrice is null', () => {
        const item = OrderFactory.createOrderItemDto({ unitPrice: null as any });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
        expect(() => OrderItemDto.validate(item)).toThrow('Valor unitário do produto é obrigatório.');
      });

      it('should throw error when unitPrice is undefined', () => {
        const item = OrderFactory.createOrderItemDto({ unitPrice: undefined as any });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
      });

      it('should throw error when unitPrice is zero', () => {
        const item = OrderFactory.createOrderItemDto({ unitPrice: 0 });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
      });

      it('should throw error when unitPrice is negative', () => {
        const item = OrderFactory.createOrderItemDto({ unitPrice: -10.5 });

        expect(() => OrderItemDto.validate(item)).toThrow(VitolaException);
      });

      it('should accept positive unitPrice', () => {
        const item = OrderFactory.createOrderItemDto({ unitPrice: 15.75 });

        expect(() => OrderItemDto.validate(item)).not.toThrow();
      });

      it('should accept decimal unitPrice', () => {
        const item = OrderFactory.createOrderItemDto({ unitPrice: 12.99 });

        expect(() => OrderItemDto.validate(item)).not.toThrow();
      });
    });

    describe('combined validation scenarios', () => {
      it('should validate item with all valid fields', () => {
        const item = OrderFactory.createOrderItemDto({
          name: 'Coca-Cola 350ml',
          description: 'Refrigerante de cola gelado',
          quantity: 3,
          unitPrice: 4.5,
        });

        expect(() => OrderItemDto.validate(item)).not.toThrow();
      });

      it('should throw first validation error encountered', () => {
        const item = OrderFactory.createOrderItemDto({
          name: '', // Invalid
          description: '', // Invalid
          quantity: 0, // Invalid
          unitPrice: 0, // Invalid
        });

        expect(() => OrderItemDto.validate(item)).toThrow('Nome do produto é obrigatório.');
      });

      it('should validate multiple items independently', () => {
        const validItem = OrderFactory.createOrderItemDto();
        const invalidItem = OrderFactory.createOrderItemDto({ name: '' });

        expect(() => OrderItemDto.validate(validItem)).not.toThrow();
        expect(() => OrderItemDto.validate(invalidItem)).toThrow();
      });
    });
  });

  describe('OrderItemDto Properties', () => {
    it('should allow setting all properties', () => {
      const item = new OrderItemDto();

      item.name = 'Test Product';
      item.description = 'Test Description';
      item.quantity = 2;
      item.unitPrice = 10.5;

      expect(item.name).toBe('Test Product');
      expect(item.description).toBe('Test Description');
      expect(item.quantity).toBe(2);
      expect(item.unitPrice).toBe(10.5);
    });
  });
});
