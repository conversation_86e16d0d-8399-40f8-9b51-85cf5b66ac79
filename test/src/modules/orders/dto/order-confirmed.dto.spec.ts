import { OrderConfirmedDto } from '../../../../../src/modules/orders/dto/order-confirmed.dto';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderFactory } from '../../../test-utils/factories/order.factory';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('OrderConfirmedDto', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('validate', () => {
    it('should validate order confirmation successfully', () => {
      const orderConfirmed = OrderFactory.createOrderConfirmedDto({
        totalPaid: 25.5,
      });

      expect(() => OrderConfirmedDto.validate(orderConfirmed)).not.toThrow();
    });

    describe('totalPaid validation', () => {
      it('should throw error when totalPaid is null', () => {
        const orderConfirmed = OrderFactory.createOrderConfirmedDto({ totalPaid: null as any });

        expect(() => OrderConfirmedDto.validate(orderConfirmed)).toThrow(VitolaException);
        expect(() => OrderConfirmedDto.validate(orderConfirmed)).toThrow('Total do pedido inválido');
      });

      it('should throw error when totalPaid is undefined', () => {
        const orderConfirmed = OrderFactory.createOrderConfirmedDto({ totalPaid: undefined as any });

        expect(() => OrderConfirmedDto.validate(orderConfirmed)).toThrow(VitolaException);
        expect(() => OrderConfirmedDto.validate(orderConfirmed)).toThrow('Total do pedido inválido');
      });

      it('should throw error when totalPaid is zero', () => {
        const orderConfirmed = OrderFactory.createOrderConfirmedDto({ totalPaid: 0 });

        expect(() => OrderConfirmedDto.validate(orderConfirmed)).toThrow(VitolaException);
        expect(() => OrderConfirmedDto.validate(orderConfirmed)).toThrow('Total do pedido inválido');
      });

      it('should throw error when totalPaid is negative', () => {
        const orderConfirmed = OrderFactory.createOrderConfirmedDto({ totalPaid: -10.5 });

        expect(() => OrderConfirmedDto.validate(orderConfirmed)).toThrow(VitolaException);
        expect(() => OrderConfirmedDto.validate(orderConfirmed)).toThrow('Total do pedido inválido');
      });

      it('should accept positive totalPaid', () => {
        const orderConfirmed = OrderFactory.createOrderConfirmedDto({ totalPaid: 15.75 });

        expect(() => OrderConfirmedDto.validate(orderConfirmed)).not.toThrow();
      });

      it('should accept decimal totalPaid', () => {
        const orderConfirmed = OrderFactory.createOrderConfirmedDto({ totalPaid: 99.99 });

        expect(() => OrderConfirmedDto.validate(orderConfirmed)).not.toThrow();
      });

      it('should throw error when orderConfirmed is null', () => {
        const nullOrderConfirmed = null as any;

        expect(() => OrderConfirmedDto.validate(nullOrderConfirmed)).toThrow(VitolaException);
        expect(() => OrderConfirmedDto.validate(nullOrderConfirmed)).toThrow('Total do pedido inválido');
      });

      it('should throw error when orderConfirmed is undefined', () => {
        const undefinedOrderConfirmed = undefined as any;

        expect(() => OrderConfirmedDto.validate(undefinedOrderConfirmed)).toThrow(VitolaException);
        expect(() => OrderConfirmedDto.validate(undefinedOrderConfirmed)).toThrow('Total do pedido inválido');
      });
    });

    describe('combined validation scenarios', () => {
      it('should validate order confirmation with valid totalPaid', () => {
        const orderConfirmed = OrderFactory.createOrderConfirmedDto({
          totalPaid: 45.75,
        });

        expect(() => OrderConfirmedDto.validate(orderConfirmed)).not.toThrow();
      });

      it('should validate multiple confirmations independently', () => {
        const validConfirmation = OrderFactory.createOrderConfirmedDto({ totalPaid: 25.5 });
        const invalidConfirmation = OrderFactory.createOrderConfirmedDto({ totalPaid: 0 });

        expect(() => OrderConfirmedDto.validate(validConfirmation)).not.toThrow();
        expect(() => OrderConfirmedDto.validate(invalidConfirmation)).toThrow();
      });

      it('should handle edge case values', () => {
        const orderConfirmed = OrderFactory.createOrderConfirmedDto({
          totalPaid: 0.01, // Minimum valid amount
        });

        expect(() => OrderConfirmedDto.validate(orderConfirmed)).not.toThrow();
      });
    });
  });

  describe('OrderConfirmedDto Properties', () => {
    it('should allow setting all properties', () => {
      const orderConfirmed = new OrderConfirmedDto();

      orderConfirmed.orderId = 'test-order-id';
      orderConfirmed.paymentProof = 'test-proof';
      orderConfirmed.confirmed = true;
      orderConfirmed.totalPaid = 50.25;

      expect(orderConfirmed.orderId).toBe('test-order-id');
      expect(orderConfirmed.paymentProof).toBe('test-proof');
      expect(orderConfirmed.confirmed).toBe(true);
      expect(orderConfirmed.totalPaid).toBe(50.25);
    });
  });
});
