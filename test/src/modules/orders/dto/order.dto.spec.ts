import { OrderItemDto } from '../../../../../src/modules/orders/dto/order-item.dto';
import { OrderDto } from '../../../../../src/modules/orders/dto/order.dto';
import { OrderStatus } from '../../../../../src/modules/orders/models/order-status';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderFactory } from '../../../test-utils/factories/order.factory';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

describe('OrderDto', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('validate', () => {
    it('should validate order successfully', () => {
      const order = OrderFactory.createOrderDto();
      delete (order as any).id;
      delete (order as any).status;
      delete (order as any).orderNumber;
      delete (order as any).total;

      OrderDto.validate(order);

      expect(order.id).toBeDefined();
      expect(order.id).toHaveValidObjectId();
      expect(order.status).toBe(OrderStatus.RECEIVED);
      expect(order.orderNumber).toBeGreaterThan(0);
      expect(order.orderNumber).toBeLessThanOrEqual(1000);
      expect(order.total).toBeGreaterThan(0);
    });

    it('should throw error when items are empty', () => {
      const order = OrderFactory.createOrderDto({ items: [] });

      expect(() => OrderDto.validate(order)).toThrow(VitolaException);
      expect(() => OrderDto.validate(order)).toThrow('Items do pedido são obrigatórios.');
    });

    it('should throw error when items are null', () => {
      const order = OrderFactory.createOrderDto({ items: null as any });

      expect(() => OrderDto.validate(order)).toThrow(VitolaException);
    });

    it('should throw error when items are undefined', () => {
      const order = OrderFactory.createOrderDto({ items: undefined as any });

      expect(() => OrderDto.validate(order)).toThrow(VitolaException);
    });

    it('should validate all items in the order', () => {
      const order = OrderFactory.createOrderWithMultipleItems(3);
      delete (order as any).id;
      delete (order as any).status;
      delete (order as any).orderNumber;
      delete (order as any).total;

      const validateSpy = jest.spyOn(OrderItemDto, 'validate').mockImplementation();

      OrderDto.validate(order);

      expect(validateSpy).toHaveBeenCalledTimes(3);
      order.items.forEach((item) => {
        expect(validateSpy).toHaveBeenCalledWith(item);
      });

      validateSpy.mockRestore();
    });

    it('should calculate total correctly for single item', () => {
      const order = OrderFactory.createOrderDto({
        items: [OrderFactory.createOrderItemDto({ quantity: 2, unitPrice: 15.5 })],
      });
      delete (order as any).total;

      OrderDto.validate(order);

      expect(order.total).toBe(31.0); // 2 * 15.50
    });

    it('should calculate total correctly for multiple items', () => {
      const order = OrderFactory.createOrderDto({
        items: [
          OrderFactory.createOrderItemDto({ quantity: 2, unitPrice: 10.0 }),
          OrderFactory.createOrderItemDto({ quantity: 1, unitPrice: 15.5 }),
          OrderFactory.createOrderItemDto({ quantity: 3, unitPrice: 5.25 }),
        ],
      });
      delete (order as any).total;

      OrderDto.validate(order);

      expect(order.total).toBe(51.25); // (2*10) + (1*15.50) + (3*5.25)
    });

    it('should generate unique order numbers', () => {
      const order1 = OrderFactory.createOrderDto();
      const order2 = OrderFactory.createOrderDto();
      delete (order1 as any).orderNumber;
      delete (order2 as any).orderNumber;

      OrderDto.validate(order1);
      OrderDto.validate(order2);

      expect(order1.orderNumber).toBeGreaterThan(0);
      expect(order2.orderNumber).toBeGreaterThan(0);
      expect(order1.orderNumber).not.toBe(order2.orderNumber);
    });

    it('should generate unique ids', () => {
      const order1 = OrderFactory.createOrderDto();
      const order2 = OrderFactory.createOrderDto();
      delete (order1 as any).id;
      delete (order2 as any).id;

      OrderDto.validate(order1);
      OrderDto.validate(order2);

      expect(order1.id).toHaveValidObjectId();
      expect(order2.id).toHaveValidObjectId();
      expect(order1.id).not.toBe(order2.id);
    });

    it('should set status to RECEIVED', () => {
      const order = OrderFactory.createOrderDto();
      delete (order as any).status;

      OrderDto.validate(order);

      expect(order.status).toBe(OrderStatus.RECEIVED);
    });
  });

  describe('validatePayment', () => {
    it('should validate payment information successfully', () => {
      const order = OrderFactory.createOrderDto({
        paymentInformation: OrderFactory.createPaymentInformation(),
      });

      expect(() => OrderDto.validatePayment(order)).not.toThrow();
    });

    it('should throw error when payment information is null', () => {
      const order = OrderFactory.createOrderDto({
        paymentInformation: null as any,
      });

      expect(() => OrderDto.validatePayment(order)).toThrow(VitolaException);
      expect(() => OrderDto.validatePayment(order)).toThrow('Pagamento do pedido é obrigatório.');
    });

    it('should throw error when payment information is undefined', () => {
      const order = OrderFactory.createOrderDto({
        paymentInformation: undefined as any,
      });

      expect(() => OrderDto.validatePayment(order)).toThrow(VitolaException);
    });
  });

  describe('calculateTotal (private method)', () => {
    it('should calculate total for zero items', () => {
      const order = OrderFactory.createOrderDto({ items: [] });

      const total = (OrderDto as any).calculateTotal(order);

      expect(total).toBe(0);
    });

    it('should handle items with zero quantity', () => {
      const order = OrderFactory.createOrderDto({
        items: [OrderFactory.createOrderItemDto({ quantity: 0, unitPrice: 10.0 })],
      });

      const total = (OrderDto as any).calculateTotal(order);

      expect(total).toBe(0);
    });

    it('should handle items with zero unit price', () => {
      const order = OrderFactory.createOrderDto({
        items: [OrderFactory.createOrderItemDto({ quantity: 2, unitPrice: 0 })],
      });

      const total = (OrderDto as any).calculateTotal(order);

      expect(total).toBe(0);
    });

    it('should handle decimal values correctly', () => {
      const order = OrderFactory.createOrderDto({
        items: [OrderFactory.createOrderItemDto({ quantity: 3, unitPrice: 10.33 }), OrderFactory.createOrderItemDto({ quantity: 2, unitPrice: 5.67 })],
      });

      const total = (OrderDto as any).calculateTotal(order);

      expect(total).toBe(42.33); // (3 * 10.33) + (2 * 5.67) = 30.99 + 11.34
    });
  });

  describe('OrderDto Properties', () => {
    it('should allow document to be optional', () => {
      const order = OrderFactory.createOrderDto({ document: undefined });

      expect(order.document).toBeUndefined();
      expect(() => OrderDto.validate(order)).not.toThrow();
    });
  });
});
