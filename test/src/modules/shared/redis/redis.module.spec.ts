import { Test, TestingModule } from '@nestjs/testing';
import { RedisConnectionService } from '../../../../../src/modules/shared/redis/redis-connection.service';
import { RedisModule } from '../../../../../src/modules/shared/redis/redis.module';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';

jest.mock('../../../../../src/modules/shared/redis/redis-connection.service');

describe('RedisModule', () => {
  let module: TestingModule;
  let redisConnectionService: RedisConnectionService;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [RedisModule],
    }).compile();

    redisConnectionService = module.get<RedisConnectionService>(RedisConnectionService);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    TestHelpers.clearAllMocks();
  });

  describe('module configuration', () => {
    it('should be defined', () => {
      expect(module).toBeDefined();
    });

    it('should provide RedisConnectionService', () => {
      expect(redisConnectionService).toBeDefined();
      expect(redisConnectionService).toBeInstanceOf(RedisConnectionService);
    });

    it('should export RedisConnectionService', () => {
      expect(redisConnectionService).toBeDefined();
    });
  });

  describe('service injection', () => {
    it('should inject RedisConnectionService successfully', () => {
      const service = module.get<RedisConnectionService>(RedisConnectionService);
      expect(service).toBeDefined();
      expect(service).toBe(redisConnectionService);
    });

    it('should be a singleton', () => {
      const service1 = module.get<RedisConnectionService>(RedisConnectionService);
      const service2 = module.get<RedisConnectionService>(RedisConnectionService);
      expect(service1).toBe(service2);
    });
  });

  describe('module imports', () => {
    it('should be importable by other modules', async () => {
      const testModule = await Test.createTestingModule({
        imports: [RedisModule],
        providers: [
          {
            provide: 'TestService',
            useFactory: (redisService: RedisConnectionService) => {
              return { redisService };
            },
            inject: [RedisConnectionService],
          },
        ],
      }).compile();
      const testService = testModule.get('TestService');
      expect(testService).toBeDefined();
      expect(testService.redisService).toBeInstanceOf(RedisConnectionService);

      await testModule.close();
    });

    it('should provide the same instance across different injections', async () => {
      const testModule = await Test.createTestingModule({
        imports: [RedisModule],
        providers: [
          {
            provide: 'Service1',
            useFactory: (redisService: RedisConnectionService) => redisService,
            inject: [RedisConnectionService],
          },
          {
            provide: 'Service2',
            useFactory: (redisService: RedisConnectionService) => redisService,
            inject: [RedisConnectionService],
          },
        ],
      }).compile();
      const service1 = testModule.get('Service1');
      const service2 = testModule.get('Service2');
      expect(service1).toBe(service2);

      await testModule.close();
    });
  });

  describe('module structure', () => {
    it('should have correct providers configuration', () => {
      expect(() => module.get<RedisConnectionService>(RedisConnectionService)).not.toThrow();
    });

    it('should not have any controllers', () => {
      const controllers = Reflect.getMetadata('controllers', RedisModule) || [];
      expect(controllers).toHaveLength(0);
    });

    it('should have RedisConnectionService in providers', () => {
      expect(redisConnectionService).toBeInstanceOf(RedisConnectionService);
    });
  });

  describe('dependency injection scenarios', () => {
    it('should handle circular dependencies gracefully', async () => {
      expect(() => {
        Test.createTestingModule({
          imports: [RedisModule, RedisModule], // Importing twice
        });
      }).not.toThrow();
    });
  });

  describe('module lifecycle', () => {
    it('should initialize without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [RedisModule],
      }).compile();
      expect(testModule).toBeDefined();

      await testModule.close();
    });

    it('should close without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [RedisModule],
      }).compile();
      await expect(testModule.close()).resolves.not.toThrow();
    });

    it('should handle multiple module instances', async () => {
      const module1 = await Test.createTestingModule({
        imports: [RedisModule],
      }).compile();

      const module2 = await Test.createTestingModule({
        imports: [RedisModule],
      }).compile();
      const service1 = module1.get<RedisConnectionService>(RedisConnectionService);
      const service2 = module2.get<RedisConnectionService>(RedisConnectionService);
      expect(service1).toBeDefined();
      expect(service2).toBeDefined();
      expect(service1).not.toBe(service2);

      await module1.close();
      await module2.close();
    });
  });

  describe('error handling', () => {
    it('should handle service instantiation errors gracefully', async () => {
      const MockRedisConnectionService = jest.fn().mockImplementation(() => {
        throw new Error('Redis connection failed');
      });

      await expect(
        Test.createTestingModule({
          providers: [
            {
              provide: RedisConnectionService,
              useClass: MockRedisConnectionService,
            },
          ],
        }).compile(),
      ).rejects.toThrow();
    });

    it('should not affect other services if Redis fails', async () => {
      class OtherService {
        getValue() {
          return 'test-value';
        }
      }

      const testModule = await Test.createTestingModule({
        imports: [RedisModule],
        providers: [OtherService],
      }).compile();
      const otherService = testModule.get<OtherService>(OtherService);
      expect(otherService).toBeDefined();
      expect(otherService.getValue()).toBe('test-value');

      await testModule.close();
    });
  });
});
