import { Logger } from '@nestjs/common';
import { createClient, RedisClientType } from 'redis';
import { RedisConnectionService } from '../../../../../src/modules/shared/redis/redis-connection.service';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';
import { createMockRedisClient } from '../../../test-utils/mocks/redis.mock';

jest.mock('redis', () => ({
  createClient: jest.fn(),
}));

describe('RedisConnectionService', () => {
  let service: RedisConnectionService;
  let mockRedisClient: jest.Mocked<RedisClientType>;
  let loggerErrorSpy: jest.SpyInstance;
  let createClientMock: jest.MockedFunction<typeof createClient>;

  beforeEach(() => {
    mockRedisClient = createMockRedisClient();

    createClientMock = createClient as jest.MockedFunction<typeof createClient>;
    createClientMock.mockReturnValue(mockRedisClient as any);

    loggerErrorSpy = jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});

    process.env.REDIS_URI = 'redis://localhost:6379';
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create Redis client with correct URI', () => {
      service = new RedisConnectionService();
      expect(createClient).toHaveBeenCalledWith({ url: 'redis://localhost:6379' });
    });

    it('should use REDIS_URI from environment', () => {
      const customUri = 'redis://custom-host:6380';
      process.env.REDIS_URI = customUri;
      service = new RedisConnectionService();
      expect(createClient).toHaveBeenCalledWith({ url: customUri });
    });
  });

  describe('connect', () => {
    beforeEach(() => {
      service = new RedisConnectionService();
    });

    it('should set connected to true on successful connection', async () => {
      mockRedisClient.connect.mockResolvedValue(mockRedisClient);
      await new Promise((resolve) => setTimeout(resolve, 10)); // Wait for async connect
      expect(service.clientIsConnected()).toBe(true);
    });

    it('should set connected to false on connection error', async () => {
      const error = new Error('Connection failed');
      mockRedisClient.connect.mockRejectedValue(error);
      service = new RedisConnectionService();
      await new Promise((resolve) => setTimeout(resolve, 10)); // Wait for async connect
      expect(service.clientIsConnected()).toBe(false);
    });

    it('should log error on connection failure', async () => {
      const error = new Error('Connection failed');
      mockRedisClient.connect.mockRejectedValue(error);
      service = new RedisConnectionService();
      await new Promise((resolve) => setTimeout(resolve, 10)); // Wait for async connect
      expect(loggerErrorSpy).toHaveBeenCalledWith('Ocorreu um erro ao conectar ao cliente do Redis: Connection failed');
    });

    it('should handle connection error without message', async () => {
      const error = new Error();
      mockRedisClient.connect.mockRejectedValue(error);
      service = new RedisConnectionService();
      await new Promise((resolve) => setTimeout(resolve, 10)); // Wait for async connect
      expect(loggerErrorSpy).toHaveBeenCalledWith('Ocorreu um erro ao conectar ao cliente do Redis: ');
    });

    it('should handle non-Error exceptions', async () => {
      const error = 'String error';
      mockRedisClient.connect.mockRejectedValue(error);
      service = new RedisConnectionService();
      await new Promise((resolve) => setTimeout(resolve, 10)); // Wait for async connect
      expect(service.clientIsConnected()).toBe(false);
    });
  });

  describe('clientIsConnected', () => {
    beforeEach(() => {
      service = new RedisConnectionService();
    });

    it('should return true when connected', async () => {
      mockRedisClient.connect.mockResolvedValue(mockRedisClient);
      await new Promise((resolve) => setTimeout(resolve, 10)); // Wait for async connect
      const isConnected = service.clientIsConnected();
      expect(isConnected).toBe(true);
    });

    it('should return false initially before connection attempt completes', () => {
      mockRedisClient.connect.mockImplementation(() => new Promise((resolve) => setTimeout(() => resolve(mockRedisClient), 100)));
      service = new RedisConnectionService();
      const isConnected = service.clientIsConnected();
      expect(isConnected).toBe(false);
    });
  });

  describe('getClient', () => {
    beforeEach(() => {
      service = new RedisConnectionService();
    });

    it('should return the Redis client', () => {
      const client = service.getClient();
      expect(client).toBe(mockRedisClient);
    });

    it('should return same client instance on multiple calls', () => {
      const client1 = service.getClient();
      const client2 = service.getClient();
      expect(client1).toBe(client2);
      expect(client1).toBe(mockRedisClient);
    });

    it('should return client regardless of connection status', async () => {
      mockRedisClient.connect.mockRejectedValue(new Error('Failed'));
      await new Promise((resolve) => setTimeout(resolve, 10)); // Wait for async connect
      const client = service.getClient();
      expect(client).toBe(mockRedisClient);
    });
  });

  describe('environment variable handling', () => {
    it('should handle undefined REDIS_URI', () => {
      delete process.env.REDIS_URI;
      service = new RedisConnectionService();
      expect(createClient).toHaveBeenCalledWith({ url: undefined });
    });

    it('should handle empty REDIS_URI', () => {
      process.env.REDIS_URI = '';
      service = new RedisConnectionService();
      expect(createClient).toHaveBeenCalledWith({ url: '' });
    });

    it('should handle REDIS_URI with authentication', () => {
      const uriWithAuth = 'redis://user:password@localhost:6379';
      process.env.REDIS_URI = uriWithAuth;
      service = new RedisConnectionService();
      expect(createClient).toHaveBeenCalledWith({ url: uriWithAuth });
    });

    it('should handle REDIS_URI with database number', () => {
      const uriWithDb = 'redis://localhost:6379/1';
      process.env.REDIS_URI = uriWithDb;
      service = new RedisConnectionService();
      expect(createClient).toHaveBeenCalledWith({ url: uriWithDb });
    });
  });

  describe('error handling edge cases', () => {
    it('should handle connection timeout', async () => {
      const timeoutError = new Error('Connection timeout');
      timeoutError.name = 'TimeoutError';
      mockRedisClient.connect.mockRejectedValue(timeoutError);
      service = new RedisConnectionService();
      await new Promise((resolve) => setTimeout(resolve, 10)); // Wait for async connect
      expect(service.clientIsConnected()).toBe(false);
      expect(loggerErrorSpy).toHaveBeenCalledWith('Ocorreu um erro ao conectar ao cliente do Redis: Connection timeout');
    });

    it('should handle network errors', async () => {
      const networkError = new Error('ECONNREFUSED');
      networkError.name = 'NetworkError';
      mockRedisClient.connect.mockRejectedValue(networkError);
      service = new RedisConnectionService();
      await new Promise((resolve) => setTimeout(resolve, 10)); // Wait for async connect
      expect(service.clientIsConnected()).toBe(false);
      expect(loggerErrorSpy).toHaveBeenCalledWith('Ocorreu um erro ao conectar ao cliente do Redis: ECONNREFUSED');
    });
  });
});
