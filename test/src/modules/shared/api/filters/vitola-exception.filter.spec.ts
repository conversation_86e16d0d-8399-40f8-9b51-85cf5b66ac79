import { ArgumentsHost, Logger } from '@nestjs/common';
import { Response } from 'express';
import { VitolaException } from '../../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { VitolaExceptionFilter } from '../../../../../../src/modules/shared/api/filters/vitola-exception.filter';
import { TestHelpers } from '../../../../test-utils/helpers/test.helpers';

describe('VitolaExceptionFilter', () => {
  let filter: VitolaExceptionFilter;
  let mockResponse: jest.Mocked<Response>;
  let mockArgumentsHost: jest.Mocked<ArgumentsHost>;
  let loggerSpy: jest.SpyInstance;

  beforeEach(() => {
    filter = new VitolaExceptionFilter();

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    } as any;

    const mockHttpArgumentsHost = {
      getResponse: jest.fn().mockReturnValue(mockResponse),
      getRequest: jest.fn(),
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue(mockHttpArgumentsHost),
      getArgs: jest.fn(),
      getArgByIndex: jest.fn(),
      switchToRpc: jest.fn(),
      switchToWs: jest.fn(),
      getType: jest.fn(),
    };

    loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('catch', () => {
    it('should handle VitolaException correctly', () => {
      const errorCode = 'TEST_ERROR';
      const errorMessage = 'Test error message';
      const traceId = 'trace-123';
      const exception = VitolaException.ofValidation(errorCode, errorMessage, undefined, traceId);
      filter.catch(exception, mockArgumentsHost);
      expect(mockArgumentsHost.switchToHttp).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(exception.getStatus());
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        return: null,
        trace: traceId,
        error: errorMessage,
        errorCode: errorCode,
      });
    });

    it('should log error message', () => {
      const errorMessage = 'Test error for logging';
      const exception = VitolaException.ofError('LOG_TEST', errorMessage);
      filter.catch(exception, mockArgumentsHost);
      expect(loggerSpy).toHaveBeenCalledWith(errorMessage);
    });

    it('should handle exception without trace ID', () => {
      const errorCode = 'NO_TRACE';
      const errorMessage = 'Error without trace';
      const exception = VitolaException.ofValidation(errorCode, errorMessage);
      filter.catch(exception, mockArgumentsHost);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        return: null,
        trace: exception.getTraceId(), // Should be empty string or undefined
        error: errorMessage,
        errorCode: errorCode,
      });
    });

    it('should use correct HTTP status from exception', () => {
      const exception = VitolaException.ofNotFound('NOT_FOUND', 'Resource not found');
      filter.catch(exception, mockArgumentsHost);
      expect(mockResponse.status).toHaveBeenCalledWith(404);
    });

    it('should handle validation exception with BAD_REQUEST status', () => {
      const exception = VitolaException.ofValidation('VALIDATION_ERROR', 'Validation failed');
      filter.catch(exception, mockArgumentsHost);
      expect(mockResponse.status).toHaveBeenCalledWith(400);
    });

    it('should handle internal error exception with INTERNAL_SERVER_ERROR status', () => {
      const exception = VitolaException.ofError('INTERNAL_ERROR', 'Internal server error');
      filter.catch(exception, mockArgumentsHost);
      expect(mockResponse.status).toHaveBeenCalledWith(500);
    });
  });

  describe('response structure', () => {
    it('should always return success: false', () => {
      const exception = VitolaException.ofValidation('TEST', 'Test');
      filter.catch(exception, mockArgumentsHost);
      const callArgs = mockResponse.json.mock.calls[0][0];
      expect(callArgs.success).toBe(false);
    });

    it('should always return return: null', () => {
      const exception = VitolaException.ofValidation('TEST', 'Test');
      filter.catch(exception, mockArgumentsHost);
      const callArgs = mockResponse.json.mock.calls[0][0];
      expect(callArgs.return).toBeNull();
    });

    it('should include all required fields in response', () => {
      const exception = VitolaException.ofValidation('TEST', 'Test', undefined, 'trace');
      filter.catch(exception, mockArgumentsHost);
      const callArgs = mockResponse.json.mock.calls[0][0];
      expect(callArgs).toHaveProperty('success');
      expect(callArgs).toHaveProperty('return');
      expect(callArgs).toHaveProperty('trace');
      expect(callArgs).toHaveProperty('error');
      expect(callArgs).toHaveProperty('errorCode');
    });
  });

  describe('error handling edge cases', () => {
    it('should handle exception with empty error message', () => {
      const exception = VitolaException.ofValidation('EMPTY_MSG', '');
      filter.catch(exception, mockArgumentsHost);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: '',
        }),
      );
    });

    it('should handle exception with empty error code', () => {
      const exception = VitolaException.ofValidation('', 'Message without code');
      filter.catch(exception, mockArgumentsHost);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          errorCode: '',
        }),
      );
    });
  });
});
