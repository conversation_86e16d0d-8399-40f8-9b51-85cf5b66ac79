import { HttpStatus } from '@nestjs/common';
import { VitolaException } from '../../../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { TestHelpers } from '../../../../../test-utils/helpers/test.helpers';

describe('VitolaException', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('ofValidation', () => {
    it('should create validation exception with required parameters', () => {
      const errorCode = 'VALIDATION_ERROR';
      const errorMessage = 'Validation failed';
      const exception = VitolaException.ofValidation(errorCode, errorMessage);
      expect(exception).toBeInstanceOf(VitolaException);
      expect(exception.getErrorCode()).toBe(errorCode);
      expect(exception.getMessage()).toBe(errorMessage);
      expect(exception.getStatus()).toBe(HttpStatus.BAD_REQUEST);
    });

    it('should create validation exception with optional parameters', () => {
      const errorCode = 'VALIDATION_ERROR';
      const errorMessage = 'Validation failed';
      const contextName = 'TestContext';
      const traceId = 'trace-123';
      const exception = VitolaException.ofValidation(errorCode, errorMessage, contextName, traceId);
      expect(exception.getErrorCode()).toBe(errorCode);
      expect(exception.getMessage()).toBe(errorMessage);
      expect(exception.getStatus()).toBe(HttpStatus.BAD_REQUEST);
      expect(exception.getTraceId()).toBe(traceId);
    });
  });

  describe('ofError', () => {
    it('should create error exception with required parameters', () => {
      const errorCode = 'INTERNAL_ERROR';
      const errorMessage = 'Internal server error';
      const exception = VitolaException.ofError(errorCode, errorMessage);
      expect(exception).toBeInstanceOf(VitolaException);
      expect(exception.getErrorCode()).toBe(errorCode);
      expect(exception.getMessage()).toBe(errorMessage);
      expect(exception.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });

    it('should create error exception with optional parameters', () => {
      const errorCode = 'INTERNAL_ERROR';
      const errorMessage = 'Internal server error';
      const contextName = 'TestContext';
      const traceId = 'trace-456';
      const exception = VitolaException.ofError(errorCode, errorMessage, contextName, traceId);
      expect(exception.getErrorCode()).toBe(errorCode);
      expect(exception.getMessage()).toBe(errorMessage);
      expect(exception.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(exception.getTraceId()).toBe(traceId);
    });
  });

  describe('ofNotFound', () => {
    it('should create not found exception with required parameters', () => {
      const errorCode = 'NOT_FOUND';
      const errorMessage = 'Resource not found';
      const exception = VitolaException.ofNotFound(errorCode, errorMessage);
      expect(exception).toBeInstanceOf(VitolaException);
      expect(exception.getErrorCode()).toBe(errorCode);
      expect(exception.getMessage()).toBe(errorMessage);
      expect(exception.getStatus()).toBe(HttpStatus.NOT_FOUND);
    });

    it('should create not found exception with optional parameters', () => {
      const errorCode = 'NOT_FOUND';
      const errorMessage = 'Resource not found';
      const contextName = 'TestContext';
      const traceId = 'trace-789';
      const exception = VitolaException.ofNotFound(errorCode, errorMessage, contextName, traceId);
      expect(exception.getErrorCode()).toBe(errorCode);
      expect(exception.getMessage()).toBe(errorMessage);
      expect(exception.getStatus()).toBe(HttpStatus.NOT_FOUND);
      expect(exception.getTraceId()).toBe(traceId);
    });
  });

  describe('ofServiceResultModel', () => {
    it('should create business exception from service result model', () => {
      const model = {
        isBusinessException: true,
        errorCode: 'BUSINESS_ERROR',
        errorMessage: 'Business logic error',
        contextName: 'ServiceContext',
      };
      const exception = VitolaException.ofServiceResultModel(model);
      expect(exception).toBeInstanceOf(VitolaException);
      expect(exception.getErrorCode()).toBe(model.errorCode);
      expect(exception.getMessage()).toBe(model.errorMessage);
      expect(exception.getStatus()).toBe(HttpStatus.BAD_REQUEST);
    });

    it('should create internal error from service result model', () => {
      const model = {
        isBusinessException: false,
        errorCode: 'SERVICE_ERROR',
        errorMessage: 'Service error',
        contextName: 'ServiceContext',
      };
      const exception = VitolaException.ofServiceResultModel(model);
      expect(exception).toBeInstanceOf(VitolaException);
      expect(exception.getErrorCode()).toBe(model.errorCode);
      expect(exception.getMessage()).toBe(model.errorMessage);
      expect(exception.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });
  });

  describe('fluent methods', () => {
    it('should set message using withMessage', () => {
      const message = 'Test message';
      const exception = new VitolaException();
      const result = exception.withMessage(message);
      expect(result).toBe(exception); // Should return same instance
      expect(exception.getMessage()).toBe(message);
    });

    it('should set HTTP status using withHttpStatus', () => {
      const status = HttpStatus.FORBIDDEN;
      const exception = new VitolaException();
      const result = exception.withHttpStatus(status);
      expect(result).toBe(exception); // Should return same instance
      expect(exception.getStatus()).toBe(status);
    });

    it('should set default HTTP status when none provided', () => {
      const exception = new VitolaException();
      exception.withHttpStatus();
      expect(exception.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });

    it('should set trace ID using withTrace', () => {
      const traceId = 'trace-abc123';
      const exception = new VitolaException();
      const result = exception.withTrace(traceId);
      expect(result).toBe(exception); // Should return same instance
      expect(exception.getTraceId()).toBe(traceId);
    });

    it('should set empty trace ID when none provided', () => {
      const exception = new VitolaException();
      exception.withTrace();
      expect(exception.getTraceId()).toBe('');
    });
  });

  describe('getter methods', () => {
    it('should return correct error code', () => {
      const errorCode = 'TEST_CODE';
      const exception = VitolaException.ofValidation(errorCode, 'Test message');
      expect(exception.getErrorCode()).toBe(errorCode);
    });

    it('should return correct message', () => {
      const message = 'Test message';
      const exception = VitolaException.ofValidation('TEST_CODE', message);
      expect(exception.getMessage()).toBe(message);
    });

    it('should return correct trace ID', () => {
      const traceId = 'test-trace';
      const exception = VitolaException.ofValidation('TEST_CODE', 'Test message', undefined, traceId);
      expect(exception.getTraceId()).toBe(traceId);
    });
  });

  describe('method chaining', () => {
    it('should support method chaining', () => {
      const errorCode = 'CHAIN_TEST';
      const message = 'Chain test message';
      const traceId = 'chain-trace';
      const status = HttpStatus.UNAUTHORIZED;
      const exception = new VitolaException().withErrorCode(errorCode).withMessage(message).withTrace(traceId).withHttpStatus(status);
      expect(exception.getErrorCode()).toBe(errorCode);
      expect(exception.getMessage()).toBe(message);
      expect(exception.getTraceId()).toBe(traceId);
      expect(exception.getStatus()).toBe(status);
    });
  });
});
