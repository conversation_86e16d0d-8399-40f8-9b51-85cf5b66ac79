import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { Test, TestingModule } from '@nestjs/testing';
import { AnyExceptionFilter } from '../../../../../../src/modules/shared/api/filters/exception.filter';
import { VitolaExceptionFilter } from '../../../../../../src/modules/shared/api/filters/vitola-exception.filter';
import { ApiReturnInterceptor } from '../../../../../../src/modules/shared/api/interceptors/api-return.interceptor';
import { SharedApiModule } from '../../../../../../src/modules/shared/api/modules/shared-api.module';
import { TestHelpers } from '../../../../test-utils/helpers/test.helpers';

describe('SharedApiModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [SharedApiModule],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
    TestHelpers.clearAllMocks();
  });

  describe('module configuration', () => {
    it('should be defined', () => {
      expect(module).toBeDefined();
    });

    it('should not have any controllers', () => {
      const controllers = Reflect.getMetadata('controllers', SharedApiModule) || [];
      expect(controllers).toHaveLength(0);
    });

    it('should not have any imports', () => {
      const imports = Reflect.getMetadata('imports', SharedApiModule) || [];
      expect(imports).toHaveLength(0);
    });
  });

  describe('provider configuration', () => {
    it('should have correct number of providers', () => {
      const providers = Reflect.getMetadata('providers', SharedApiModule) || [];
      expect(providers).toHaveLength(3);
    });

    it('should configure VitolaExceptionFilter with correct token', () => {
      const providers = Reflect.getMetadata('providers', SharedApiModule) || [];
      const vitolaFilterProvider = providers.find((provider: any) => provider.provide === APP_FILTER && provider.useClass === VitolaExceptionFilter);

      expect(vitolaFilterProvider).toBeDefined();
      expect(vitolaFilterProvider.provide).toBe(APP_FILTER);
      expect(vitolaFilterProvider.useClass).toBe(VitolaExceptionFilter);
    });

    it('should configure AnyExceptionFilter with correct token', () => {
      const providers = Reflect.getMetadata('providers', SharedApiModule) || [];
      const anyFilterProvider = providers.find((provider: any) => provider.provide === APP_FILTER && provider.useClass === AnyExceptionFilter);

      expect(anyFilterProvider).toBeDefined();
      expect(anyFilterProvider.provide).toBe(APP_FILTER);
      expect(anyFilterProvider.useClass).toBe(AnyExceptionFilter);
    });

    it('should configure ApiReturnInterceptor with correct token', () => {
      const providers = Reflect.getMetadata('providers', SharedApiModule) || [];
      const interceptorProvider = providers.find((provider: any) => provider.provide === APP_INTERCEPTOR && provider.useClass === ApiReturnInterceptor);

      expect(interceptorProvider).toBeDefined();
      expect(interceptorProvider.provide).toBe(APP_INTERCEPTOR);
      expect(interceptorProvider.useClass).toBe(ApiReturnInterceptor);
    });
  });

  describe('module imports', () => {
    it('should be importable by other modules', async () => {
      const testModule = await Test.createTestingModule({
        imports: [SharedApiModule],
        controllers: [],
        providers: [],
      }).compile();
      expect(testModule).toBeDefined();

      await testModule.close();
    });

    it('should work with application modules', async () => {
      class TestController {
        getTest() {
          return 'test';
        }
      }

      const testModule = await Test.createTestingModule({
        imports: [SharedApiModule],
        controllers: [TestController],
      }).compile();
      const controller = testModule.get<TestController>(TestController);
      expect(controller).toBeDefined();
      expect(controller.getTest()).toBe('test');

      await testModule.close();
    });

    it('should not conflict with multiple imports', async () => {
      const testModule = await Test.createTestingModule({
        imports: [SharedApiModule, SharedApiModule], // Importing twice
      }).compile();
      expect(testModule).toBeDefined();

      await testModule.close();
    });
  });

  describe('global providers behavior', () => {
    it('should make filters available globally', async () => {
      // This test verifies that the filters are registered globally
      // by creating an app context and checking if they're applied
      const app = module.createNestApplication();

      // The filters should be automatically applied to all routes
      expect(app).toBeDefined();

      await app.close();
    });

    it('should make interceptor available globally', async () => {
      // This test verifies that the interceptor is registered globally
      const app = module.createNestApplication();

      // The interceptor should be automatically applied to all routes
      expect(app).toBeDefined();

      await app.close();
    });
  });

  describe('module lifecycle', () => {
    it('should initialize without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [SharedApiModule],
      }).compile();
      expect(testModule).toBeDefined();

      // Cleanup
      await testModule.close();
    });

    it('should close without errors', async () => {
      const testModule = await Test.createTestingModule({
        imports: [SharedApiModule],
      }).compile();
      await expect(testModule.close()).resolves.not.toThrow();
    });

    it('should handle multiple instances', async () => {
      const module1 = await Test.createTestingModule({
        imports: [SharedApiModule],
      }).compile();

      const module2 = await Test.createTestingModule({
        imports: [SharedApiModule],
      }).compile();
      expect(module1).toBeDefined();
      expect(module2).toBeDefined();

      // Cleanup
      await module1.close();
      await module2.close();
    });
  });

  describe('integration scenarios', () => {
    it('should work with controllers that throw exceptions', async () => {
      class TestController {
        throwError() {
          throw new Error('Test error');
        }
      }

      const testModule = await Test.createTestingModule({
        imports: [SharedApiModule],
        controllers: [TestController],
      }).compile();
      const controller = testModule.get<TestController>(TestController);
      expect(controller).toBeDefined();
      expect(() => controller.throwError()).toThrow('Test error');

      await testModule.close();
    });

    it('should work with controllers that return data', async () => {
      class TestController {
        getData() {
          return { message: 'success' };
        }
      }

      const testModule = await Test.createTestingModule({
        imports: [SharedApiModule],
        controllers: [TestController],
      }).compile();
      const controller = testModule.get<TestController>(TestController);
      expect(controller).toBeDefined();
      expect(controller.getData()).toEqual({ message: 'success' });

      await testModule.close();
    });
  });

  describe('error handling', () => {
    it('should not fail if filters cannot be instantiated', () => {
      expect(() => {
        const providers = Reflect.getMetadata('providers', SharedApiModule) || [];
        providers.forEach((provider: any) => {
          if (provider.useClass) {
            // Verify the classes can be instantiated
            new provider.useClass();
          }
        });
      }).not.toThrow();
    });

    it('should handle module compilation errors gracefully', async () => {
      await expect(
        Test.createTestingModule({
          imports: [SharedApiModule],
        }).compile(),
      ).resolves.toBeDefined();
    });
  });
});
