import { Types } from 'mongoose';
import { generateNewObjectId, objectIdFromStringValue, GPObjectIdType } from '../../../../../../src/modules/shared/database/helpers/generate-objectId';
import { TestHelpers } from '../../../../test-utils/helpers/test.helpers';

describe('generate-objectId helpers', () => {
  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('generateNewObjectId', () => {
    it('should generate a new ObjectId', () => {
      const objectId = generateNewObjectId();
      expect(objectId).toBeInstanceOf(Types.ObjectId);
      expect(objectId.toString()).toHaveValidObjectId();
    });

    it('should generate unique ObjectIds', () => {
      const objectId1 = generateNewObjectId();
      const objectId2 = generateNewObjectId();
      expect(objectId1).not.toEqual(objectId2);
      expect(objectId1.toString()).not.toBe(objectId2.toString());
    });

    it('should generate ObjectIds with correct length', () => {
      const objectId = generateNewObjectId();
      expect(objectId.toString()).toHaveLength(24);
    });

    it('should generate ObjectIds with valid hex characters', () => {
      const objectId = generateNewObjectId();
      const objectIdString = objectId.toString();
      expect(objectIdString).toMatch(/^[0-9a-fA-F]{24}$/);
    });

    it('should generate multiple unique ObjectIds in sequence', () => {
      const objectIds: Types.ObjectId[] = [];
      const count = 10;
      for (let i = 0; i < count; i++) {
        objectIds.push(generateNewObjectId());
      }
      const uniqueIds = new Set(objectIds.map((id) => id.toString()));
      expect(uniqueIds.size).toBe(count);
    });

    it('should return GPObjectIdType', () => {
      const objectId: GPObjectIdType = generateNewObjectId();
      expect(objectId).toBeInstanceOf(Types.ObjectId);
    });
  });

  describe('objectIdFromStringValue', () => {
    it('should create ObjectId from valid string', () => {
      const validObjectIdString = '507f1f77bcf86cd799439011';
      const objectId = objectIdFromStringValue(validObjectIdString);
      expect(objectId).toBeInstanceOf(Types.ObjectId);
      expect(objectId.toString()).toBe(validObjectIdString);
    });

    it('should create ObjectId from generated ObjectId string', () => {
      const originalObjectId = generateNewObjectId();
      const objectIdString = originalObjectId.toString();
      const recreatedObjectId = objectIdFromStringValue(objectIdString);
      expect(recreatedObjectId).toBeInstanceOf(Types.ObjectId);
      expect(recreatedObjectId.toString()).toBe(objectIdString);
      expect(recreatedObjectId.equals(originalObjectId)).toBe(true);
    });

    it('should throw error for invalid string', () => {
      const invalidString = 'invalid-objectid-string';
      expect(() => objectIdFromStringValue(invalidString)).toThrow();
    });

    it('should throw error for empty string', () => {
      const emptyString = '';
      expect(() => objectIdFromStringValue(emptyString)).toThrow();
    });

    it('should handle mixed case hex characters', () => {
      const mixedCaseHex = '507f1F77BcF86cD799439011';
      const objectId = objectIdFromStringValue(mixedCaseHex);
      expect(objectId).toBeInstanceOf(Types.ObjectId);
      expect(objectId.toString()).toBe(mixedCaseHex.toLowerCase());
    });

    it('should return GPObjectIdType', () => {
      const validString = '507f1f77bcf86cd799439011';
      const objectId: GPObjectIdType = objectIdFromStringValue(validString);
      expect(objectId).toBeInstanceOf(Types.ObjectId);
    });
  });

  describe('integration tests', () => {
    it('should work together - generate and recreate', () => {
      const originalObjectId = generateNewObjectId();
      const objectIdString = originalObjectId.toString();
      const recreatedObjectId = objectIdFromStringValue(objectIdString);
      expect(recreatedObjectId.equals(originalObjectId)).toBe(true);
      expect(recreatedObjectId.toString()).toBe(objectIdString);
    });

    it('should maintain ObjectId properties after conversion', () => {
      const originalObjectId = generateNewObjectId();
      const objectIdString = originalObjectId.toString();
      const recreatedObjectId = objectIdFromStringValue(objectIdString);
      expect(recreatedObjectId.getTimestamp()).toEqual(originalObjectId.getTimestamp());
      expect(recreatedObjectId.toHexString()).toBe(originalObjectId.toHexString());
    });

    it('should handle multiple conversions', () => {
      const count = 5;
      const originalIds = Array.from({ length: count }, () => generateNewObjectId());
      const convertedIds = originalIds.map((id) => objectIdFromStringValue(id.toString()));
      for (let i = 0; i < count; i++) {
        expect(convertedIds[i].equals(originalIds[i])).toBe(true);
      }
    });
  });

  describe('type definitions', () => {
    it('should export GPObjectIdType as Types.ObjectId', () => {
      const objectId = generateNewObjectId();
      const typedObjectId: GPObjectIdType = objectId;
      expect(typedObjectId).toBeInstanceOf(Types.ObjectId);
    });

    it('should be compatible with Mongoose Types.ObjectId', () => {
      const generatedId = generateNewObjectId();
      const convertedId = objectIdFromStringValue(generatedId.toString());
      const mongooseObjectId: Types.ObjectId = generatedId;
      const gpObjectId: GPObjectIdType = convertedId;
      expect(mongooseObjectId).toBeInstanceOf(Types.ObjectId);
      expect(gpObjectId).toBeInstanceOf(Types.ObjectId);
      expect(mongooseObjectId.equals(gpObjectId)).toBe(true);
    });
  });

  describe('edge cases', () => {
    it('should handle ObjectId created at different times', () => {
      const id1 = generateNewObjectId();

      // Wait a small amount to ensure different timestamp
      const id2 = generateNewObjectId();
      expect(id1.toString()).not.toBe(id2.toString());
      expect(id1.equals(id2)).toBe(false);
    });

    it('should handle string with leading/trailing whitespace', () => {
      const validId = generateNewObjectId().toString();
      const idWithWhitespace = `  ${validId}  `;
      expect(() => objectIdFromStringValue(idWithWhitespace)).toThrow();
    });

    it('should handle string that is too short', () => {
      const shortString = '507f1f77bcf86cd79943901';
      expect(() => objectIdFromStringValue(shortString)).toThrow();
    });

    it('should handle string that is too long', () => {
      const longString = '507f1f77bcf86cd7994390111';
      expect(() => objectIdFromStringValue(longString)).toThrow();
    });
  });
});
