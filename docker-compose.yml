services:
  sonarqube:
    image: sonarqube:community
    container_name: sonarqube
    ports:
      - '9000:9000'
    networks:
      - mynetwork
    environment:
      - SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true
      - SONAR_JAVA_PATH=/opt/java/openjdk/bin/java
      - sonar.search.javaAdditionalOpts=-Dnode.store.allow_mmap=false -Dorg.elasticsearch.nativeaccess=false
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
    restart: unless-stopped

  mongodb:
    image: mongo:4.4
    container_name: mongodb
    ports:
      - '27017:27017'
    networks:
      - mynetwork
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD}
    volumes:
      - ./init-mongo:/docker-entrypoint-initdb.d:ro
      - mongodb_data:/data/db
      - ./mongod.conf:/etc/mongo/mongod.conf:ro
    command: ['mongod', '--config', '/etc/mongo/mongod.conf']

  redis:
    image: redis:7.2-alpine
    container_name: redis
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: always

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vitola-lanches-api
    ports:
      - '3000:3000'
    environment:
      MONGO_URI: ${MONGO_URI}
      MERCADO_PAGO_ACCESSTOKEN: ${MERCADO_PAGO_ACCESSTOKEN}
      REDIS_URI: ${REDIS_URI}
    depends_on:
      - mongodb
    networks:
      - mynetwork
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
      - /usr/src/app/.build

networks:
  mynetwork:

volumes:
  mongodb_data:
  redis_data:
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
