import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MercadoPagoModule } from './modules/mercadopago/mercado-pago.module';
import { OrderModule } from './modules/orders/order.module';
import { SharedApiModule } from './modules/shared/api/modules/shared-api.module';
import MongooseConnection from './modules/shared/database/connection/mongoose.connection';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MercadoPagoModule,
    MongooseConnection,
    SharedApiModule,
    OrderModule,
  ],
})
export class AppModule {}
