import { Injectable, Logger } from '@nestjs/common';
import { createClient, RedisClientType } from 'redis';

@Injectable()
export class RedisConnectionService {
  private readonly logger = new Logger(RedisConnectionService.name);

  private client: RedisClientType;
  private connected: boolean = false;

  public constructor() {
    this.client = createClient({ url: process.env.REDIS_URI });
    this.connect();
  }

  private async connect(): Promise<void> {
    try {
      this.logger.log(`Conectando ao cliente do Redis`);
      await this.client.connect();
      this.connected = true;
    } catch (error) {
      this.connected = false;
      this.logger.error(`Ocorreu um erro ao conectar ao cliente do Redis: ${error.message}`);
    }
  }

  public clientIsConnected(): boolean {
    return this.connected;
  }

  public getClient(): RedisClientType {
    return this.client;
  }
}
