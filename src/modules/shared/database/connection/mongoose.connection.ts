import { Logger } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

const logger = new Logger('MongooseConnection');

type MongooseConnectionType = ReturnType<typeof MongooseModule.forRootAsync>;

const MongooseConnection: MongooseConnectionType = MongooseModule.forRootAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: async (config: ConfigService) => {
    const mongoUri = config.get<string>('MONGO_URI');
    if (!mongoUri) {
      logger.error('A variável MONGO_URI não está definida');
      throw new Error('MONGO_URI is required but not defined.');
    }

    logger.log(`Conectado ao ambiente`);

    return {
      uri: mongoUri,
      useNewUrlParser: true,
      useUnifiedTopology: true,
    };
  },
});

export default MongooseConnection;
