import { Module } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { VitolaExceptionFilter } from '../filters/vitola-exception.filter';
import { AnyExceptionFilter } from '../filters/exception.filter';
import { ApiReturnInterceptor } from '../interceptors/api-return.interceptor';

@Module({
  imports: [],
  controllers: [],
  providers: [
    {
      provide: APP_FILTER,
      useClass: VitolaExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: AnyExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ApiReturnInterceptor,
    },
  ],
})
export class SharedApiModule {}
