import { HttpException, HttpStatus, Logger } from '@nestjs/common';

export type ResponseDetail = {
  status?: number;
  statusMessage?: string;
  headers: any;
  data?: any;
};

export class VitolaException extends HttpException {
  private static readonly logger = new Logger(VitolaException.name);

  private responseDetail: ResponseDetail;
  private errorCode: string;
  private traceId: string;
  private statusCode: HttpStatus;
  private contextName: string;
  private data: any;

  constructor(message?: string) {
    super(message ?? '', 200);
    Object.setPrototypeOf(this, VitolaException.prototype);
  }

  public static ofValidation(errorCode: string, errorMessage: string, contextName?: string, traceId?: string): VitolaException {
    return new VitolaException()
      .withErrorCode(errorCode)
      .withMessage(errorMessage)
      .withHttpStatus(HttpStatus.BAD_REQUEST)
      .withContextName(contextName)
      .withTrace(traceId);
  }

  public static ofError(errorCode: string, errorMessage: string, contextName?: string, traceId?: string): VitolaException {
    return new VitolaException()
      .withErrorCode(errorCode)
      .withMessage(errorMessage)
      .withHttpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
      .withContextName(contextName)
      .withTrace(traceId);
  }

  public static ofNotFound(errorCode: string, errorMessage: string, contextName?: string, traceId?: string): VitolaException {
    return new VitolaException()
      .withErrorCode(errorCode)
      .withMessage(errorMessage)
      .withHttpStatus(HttpStatus.NOT_FOUND)
      .withContextName(contextName)
      .withTrace(traceId);
  }

  public static ofServiceResultModel(model: any): VitolaException {
    this.logError(model);

    if (model.isBusinessException) {
      return this.ofValidation(model.errorCode, model.errorMessage);
    } else {
      return this.ofError(model.errorCode, model.errorMessage);
    }
  }

  private static logError(model: any): void {
    const message = `${model.errorCode} - ${model.errorMessage}`;
    this.logger.error(message, undefined, model.contextName);
  }

  withMessage(message: string): this {
    this.message = message;
    return this;
  }

  withHttpStatus(status?: HttpStatus): this {
    this.statusCode = status ?? HttpStatus.INTERNAL_SERVER_ERROR;
    return this;
  }

  withTrace(traceId?: string): this {
    this.traceId = traceId ?? '';
    return this;
  }

  withErrorCode(errorCode: string): this {
    this.errorCode = errorCode;
    return this;
  }

  withResponse(responseDetail: ResponseDetail): this {
    this.responseDetail = responseDetail;
    return this;
  }

  withData(data?: any): this {
    this.data = data;
    return this;
  }

  withContextName(contextName?: string): this {
    this.contextName = contextName ?? '';
    return this;
  }

  getErrorCode() {
    return this.errorCode;
  }

  getTraceId() {
    return this.traceId;
  }

  getMessage() {
    return this.message;
  }

  getStatusCode() {
    return this.statusCode;
  }

  getStatus(): number {
    return Number(this.statusCode);
  }

  getResponseDetail() {
    return this.responseDetail;
  }

  getData() {
    return this.data;
  }

  getContextName() {
    return this.contextName;
  }
}
