import { ArgumentsHost, Catch, Logger } from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';
import { Response } from 'express';
import { VitolaException } from '../exceptions/models/vitola-exception-builder.model';

@Catch()
export class VitolaExceptionFilter extends BaseExceptionFilter {
  catch(exception: VitolaException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = exception.getStatus();

    const exceptionMessage: string = exception.getMessage();

    Logger.error(exceptionMessage);

    const apiReturn = {
      success: false,
      return: null,
      trace: exception.getTraceId(),
      error: exception.getMessage(),
      errorCode: exception.getErrorCode(),
    };

    return response.status(status).json(apiReturn);
  }
}
