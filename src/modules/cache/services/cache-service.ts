import { Injectable, Logger } from '@nestjs/common';

import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';
import { RedisConnectionService } from '../../shared/redis/redis-connection.service';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);

  public constructor(private readonly redisConnectionService: RedisConnectionService) {}

  public async getOrCreateObject<T>(key: string, valueProducer: () => Promise<T>, ttlInSeconds: number = 3600): Promise<T> {
    return await this.getOrCreate(key, valueProducer, ttlInSeconds, JSON.stringify, JSON.parse);
  }

  public async getOrCreateNumber(key: string, valueProducer: () => Promise<number>, ttlInSeconds: number = 3600): Promise<number | null> {
    const number = await this.getOrCreate<number>(key, valueProducer, ttlInSeconds);
    if (number == null) {
      return null;
    }

    return Number(number);
  }

  public async getOrCreateString(key: string, valueProducer: () => Promise<string>, ttlInSeconds: number = 3600): Promise<string> {
    return await this.getOrCreate<string>(key, valueProducer, ttlInSeconds);
  }

  private async getOrCreate<T>(
    key: string,
    valueProducer: () => Promise<T>,
    ttlInSeconds: number,
    serializer?: (value: T) => string,
    deserializer?: (value: string) => T,
  ): Promise<T> {
    try {
      const value = await this.get(key);
      if (value) {
        if (deserializer) {
          return deserializer(value);
        }
        return value;
      }

      const newValue = await valueProducer();
      if (newValue == null) {
        return newValue;
      }

      if (!this.redisConnectionService.clientIsConnected()) {
        return newValue;
      }

      if (serializer) {
        await this.set(key, serializer(newValue), ttlInSeconds);
      } else {
        await this.set(key, newValue as any, ttlInSeconds);
      }

      return newValue;
    } catch (error) {
      this.logger.error(`Ocorreu um erro ao buscar dados no cache: ${error.message}`);
      throw new VitolaException(error.message).withErrorCode('ERROR_CACHE_GET');
    }
  }

  private async set(key: any, value: string | number, expirationInSeconds = 0): Promise<boolean> {
    try {
      if (!this.redisConnectionService.clientIsConnected()) {
        return true;
      }

      if (expirationInSeconds > 0) {
        this.logger.log('SETTING CACHE WITH EXPIRATION: ' + expirationInSeconds);
        await this.redisConnectionService.getClient().set(key, value, { EX: expirationInSeconds });
      } else {
        this.logger.log('SETTING CACHE WITHOUT EXPIRATION');
        await this.redisConnectionService.getClient().set(key, value);
      }
    } catch (error) {
      this.logger.error(`Ocorreu um erro ao criar dados no cache: ${error.message}`);
      throw new VitolaException(error.message).withErrorCode('ERROR_CACHE_SET');
    }

    return true;
  }

  private async get(key: string): Promise<any> {
    try {
      if (this.redisConnectionService.clientIsConnected()) {
        return await this.redisConnectionService.getClient().get(key);
      }
      return null;
    } catch (error) {
      this.logger.error(`Ocorreu um erro ao buscar dados no cache: ${error.message}`);
      throw new VitolaException(error.message).withErrorCode('ERROR_CACHE_SET');
    }
  }

  private async delete(key: any): Promise<boolean> {
    try {
      await this.redisConnectionService.getClient().del(key);
    } catch (error) {
      this.logger.error(`Ocorreu um erro ao deletar dados no cache: ${error.message}`);
      throw new VitolaException(error.message).withErrorCode('ERROR_CACHE_DELETE');
    }

    return true;
  }

  private async deleteByPrefix(key: string): Promise<boolean> {
    try {
      this.logger.log(`Key ${key} - Deletando keys pelo prefixo`);
      const keys = await this.redisConnectionService.getClient().keys(`${key}*`);
      for (const deleteKey of keys) {
        await this.redisConnectionService.getClient().del(deleteKey);
      }
    } catch (error) {
      this.logger.error(`Ocorreu um erro ao deletar dados no cache: ${error.message}`);
      throw new VitolaException(error.message).withErrorCode('ERROR_CACHE_DELETE');
    }

    return true;
  }

  public async deleteBySuffix(key: string): Promise<boolean> {
    try {
      this.logger.log(`Key ${key} - Deletando keys pelo sufixo`);
      const keys = await this.redisConnectionService.getClient().keys(`*${key}`);
      for (const deleteKey of keys) {
        await this.redisConnectionService.getClient().del(deleteKey);
      }
    } catch (error) {
      this.logger.error(`Ocorreu um erro ao deletar dados no cache: ${error.message}`);
      throw new VitolaException(error.message).withErrorCode('ERROR_CACHE_DELETE');
    }

    return true;
  }

  public async clearAll(): Promise<boolean> {
    try {
      await this.redisConnectionService.getClient().flushDb();
    } catch (error) {
      this.logger.error(`Ocorreu um erro ao limpar todos os dados do cache: ${error.message}`);
      throw new VitolaException(error.message).withErrorCode('ERROR_CACHE_CLEAR');
    }

    return true;
  }
}
