import { Inject, Injectable, Logger } from '@nestjs/common';
import { MercadoPagoServicePortToken } from '../../../mercadopago/constants/mercado-pago.constants';
import { PaymentRequestDto } from '../../../mercadopago/dto/payment-request.dto';
import { MercadoPagoServicePort } from '../../../mercadopago/services/mercado-pago.service.port';
import { VitolaException } from '../../../shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderRepositoryPortToken } from '../../constants/order.constants';
import { OrderConfirmedDto } from '../../dto/order-confirmed.dto';
import { OrderDto } from '../../dto/order.dto';
import { PaymentInformation } from '../../models/order-payment-information';
import { OrderRepositoryPort } from '../repositories/order.repository.port';
import { OrderManagementServicePort as OrderRegistrationServicePort } from './order-registration.service.port';

@Injectable()
export class OrderRegistrationService implements OrderRegistrationServicePort {
  private readonly logger = new Logger(OrderRegistrationService.name);

  constructor(
    @Inject(OrderRepositoryPortToken) private readonly orderRepository: OrderRepositoryPort,
    @Inject(MercadoPagoServicePortToken) private readonly mercadoPagoService: MercadoPagoServicePort,
  ) {}

  private async create(order: OrderDto): Promise<string> {
    return await this.orderRepository.create(order);
  }

  async issueOrder(order: OrderDto): Promise<OrderDto> {
    if (!order) {
      throw VitolaException.ofValidation('ORDER_NOT_FOUND', 'Escolha ao menos 1 item para criar o pedido.');
    }

    OrderDto.validate(order);

    const paymentInfo = await this.mercadoPagoService.createPayment(new PaymentRequestDto(order.id, `Pedido: ${order.orderNumber}`, order.total, 1));

    try {
      order.paymentInformation = PaymentInformation.of(paymentInfo);
      order.id = await this.create(order);
    } catch (exception) {
      this.logger.log(`Erro durante criação do pedido: ${exception}`);
      throw VitolaException.ofValidation('ORDER_CREATION_ERROR', 'Ocorreu um erro ao criar pedido.');
    }

    return order;
  }

  async confirmOrder(orderId: string, orderConfirmed: OrderConfirmedDto): Promise<boolean> {
    if (!orderId) {
      throw VitolaException.ofValidation('ORDERID_INVALID', 'Identificação do pedido inválida');
    }

    if (!orderConfirmed) {
      throw VitolaException.ofValidation('ORDER_INVALID', 'Pedido inválida');
    }

    OrderConfirmedDto.validate(orderConfirmed);

    return await this.orderRepository.confirmOrder(orderId, orderConfirmed);
  }
}
