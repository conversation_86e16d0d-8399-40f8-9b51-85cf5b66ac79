import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { objectIdFromStringValue } from '../../../shared/database/helpers/generate-objectId';
import { UpdateVerifier } from '../../../shared/database/helpers/update-verifier';
import { OrderConfirmedDto } from '../../dto/order-confirmed.dto';
import { OrderDto } from '../../dto/order.dto';
import { OrderStatus } from '../../models/order-status';
import { Order } from '../../schema/order.schema';
import { OrderRepositoryPort } from './order.repository.port';

@Injectable()
export class OrderRepository implements OrderRepositoryPort {
  constructor(@InjectModel('Order') private readonly orderModel: Model<Order>) {}

  async confirmOrder(orderId: string, orderConfirmed: OrderConfirmedDto): Promise<boolean> {
    const updated = await this.orderModel.updateOne(
      {
        _id: objectIdFromStringValue(orderId),
      },
      {
        $set: {
          status: OrderStatus.PREPARING,
          updateDate: new Date(),
          paymentInformation: {
            paymentProof: orderConfirmed.paymentProof,
            totalPaid: orderConfirmed.totalPaid,
            confirmed: orderConfirmed.confirmed,
            updateDate: new Date(),
          },
        },
      },
    );

    return UpdateVerifier.wasUpdated(updated);
  }

  async create(order: OrderDto): Promise<string> {
    const orderData = {
      _id: order.id,
      document: order.document,
      orderNumber: order.orderNumber,
      status: order.status,
      items: order.items,
      paymentInformation: order.paymentInformation,
      total: order.total,
      createDate: new Date(),
      updateDate: new Date(),
    };

    const createdOrder = await this.orderModel.create(orderData);

    return createdOrder._id;
  }
}
