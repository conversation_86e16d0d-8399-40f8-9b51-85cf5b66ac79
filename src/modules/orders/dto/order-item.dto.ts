import { ApiProperty } from '@nestjs/swagger';
import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';

export class OrderItemDto {
  @ApiProperty({
    name: 'name',
    description: 'Nome do produto',
    example: 'Misto quente gigante',
  })
  name: string;

  @ApiProperty({
    name: 'description',
    description: 'Nome do produto',
    example: 'Muito QUEIJO e PRESUNTO!!',
  })
  description: string;

  @ApiProperty({
    name: 'quantity',
    description: 'Nome do produto',
    example: 1,
  })
  quantity: number;

  @ApiProperty({
    name: 'unitPrice',
    description: 'Nome do produto',
    example: 10,
  })
  unitPrice: number;

  static validate(item: OrderItemDto) {
    if (!item.name) {
      throw VitolaException.ofValidation('PRODUCT_NAME_NOT_FOUND', 'Nome do produto é obrigatório.');
    }

    if (!item.description) {
      throw VitolaException.ofValidation('PRODUCT_DESCRIPTION_NOT_FOUND', 'Descrição do produto é obrigatório.');
    }

    if (!item.quantity || item.quantity <= 0) {
      throw VitolaException.ofValidation('PRODUCT_QUANTITY_NOT_FOUND', 'Quantidade do produto é obrigatório.');
    }

    if (!item.unitPrice || item.unitPrice <= 0) {
      throw VitolaException.ofValidation('PRODUCT_UNIT_NOT_FOUND', 'Valor unitário do produto é obrigatório.');
    }
  }
}
