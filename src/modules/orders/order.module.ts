import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MercadoPagoModule } from '../mercadopago/mercado-pago.module';
import MongooseConnection from '../shared/database/connection/mongoose.connection';
import { OrderManagementServicePortToken, OrderRepositoryPortToken } from './constants/order.constants';
import { OrderRegistrationService } from './control/services/order-registration.service';
import { OrderController } from './controllers/order.controller';
import { Order, OrderSchema } from './schema/order.schema';
import { OrderRepository } from './control/repositories/order.repository';

const MODELS = MongooseModule.forFeature([{ name: Order.name, schema: OrderSchema }]);

@Module({
  imports: [MODELS, MongooseConnection, MercadoPagoModule],
  providers: [
    { provide: OrderRepositoryPortToken, useClass: OrderRepository },
    { provide: OrderManagementServicePortToken, useClass: OrderRegistrationService },
  ],
  controllers: [OrderController],
})
export class OrderModule {}
