import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { generateNewObjectId } from '../../shared/database/helpers/generate-objectId';
import { PaymentInformation } from '../models/order-payment-information';
import { OrderStatus } from '../models/order-status';
import { OrderItem, OrderItemSchema } from './order-item.schema';

@Schema({
  collection: 'Orders',
  versionKey: false,
  _id: true,
})
export class Order extends Document {
  @Prop({ type: Types.ObjectId, default: () => generateNewObjectId(), required: true })
  _id: string;

  @Prop({
    type: String,
    unique: false,
  })
  document: string;

  @Prop({
    type: Number,
    unique: true,
  })
  orderNumber: number;

  @Prop({
    default: () => new Date(),
  })
  createDate: Date;

  @Prop({
    type: Date,
    default: () => new Date(),
  })
  updateDate: Date;

  @Prop({
    type: String,
    enum: [OrderStatus.RECEIVED, OrderStatus.PREPARING, OrderStatus.READY, OrderStatus.COMPLETED, OrderStatus.CANCELED, OrderStatus.DISCARDED],
  })
  status: OrderStatus;

  @Prop({
    type: [OrderItemSchema],
  })
  items: OrderItem[];

  @Prop({
    type: MongooseSchema.Types.Mixed,
  })
  paymentInformation: PaymentInformation;

  @Prop({
    type: Types.Decimal128,
  })
  total: number;
}

export const OrderSchema = SchemaFactory.createForClass(Order);
