import { Body, Controller, Inject, Param, Post, Put } from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { OrderDtoMockDocumentation, OrderManagementServicePortToken } from '../constants/order.constants';
import { OrderConfirmedDto } from '../dto/order-confirmed.dto';
import { OrderDto } from '../dto/order.dto';
import { OrderManagementServicePort } from '../control/services/order-registration.service.port';

@ApiTags('Pedidos')
@Controller('orders')
export class OrderController {
  constructor(@Inject(OrderManagementServicePortToken) private readonly orderService: OrderManagementServicePort) {}

  @Post('issue')
  @ApiResponse({ status: 201, description: 'Pedido criado com sucesso', schema: { example: { success: true, return: OrderDtoMockDocumentation } } })
  async issueOrder(@Body() order: OrderDto): Promise<OrderDto> {
    return await this.orderService.issueOrder(order);
  }

  @Put(':orderId/confirm')
  @ApiResponse({ status: 200, description: 'Pedido confirmado com sucesso', schema: { example: { success: true, return: true } } })
  @ApiResponse({
    status: 500,
    description: 'Id do pedido não informado',
    schema: {
      example: { success: false, return: { errorCode: 'ORDERID_IS_REQUIRED', message: 'Id do pedido inválido' } },
    },
  })
  async confirmeOrder(@Param('orderId') orderId: string, @Body() orderConfirmed: OrderConfirmedDto): Promise<boolean> {
    return await this.orderService.confirmOrder(orderId, orderConfirmed);
  }
}
