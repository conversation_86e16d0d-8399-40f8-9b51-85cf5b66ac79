# 🍔 Vitola Lanches - API de Pagamentos

API REST desenvolvida em NestJS para gerenciamento de pedidos e pagamentos da lanchonete Vitola Lanches, integrada com o Mercado Pago para processamento de pagamentos via PIX.

## 📋 Índice

- [Sobre o Projeto](#sobre-o-projeto)
- [Tecnologias](#tecnologias)
- [Arquitetura](#arquitetura)
- [Pré-requisitos](#pré-requisitos)
- [Instalação](#instalação)
- [Configuração](#configuração)
- [Executando a Aplicação](#executando-a-aplicação)
- [Testes](#testes)
- [API Endpoints](#api-endpoints)
- [Docker](#docker)
- [Qualidade de Código](#qualidade-de-código)
- [Contribuição](#contribuição)

## 🎯 Sobre o Projeto

A API de Pagamentos da Vitola Lanches é um sistema completo para gerenciamento de pedidos de uma lanchonete, oferecendo:

- **Criação de Pedidos**: Registro de novos pedidos com itens personalizados
- **Integração com Mercado Pago**: Geração automática de pagamentos PIX
- **Confirmação de Pagamentos**: Validação e confirmação de pagamentos recebidos

## 🚀 Tecnologias

### Backend

- **[NestJS](https://nestjs.com/)** - Framework Node.js progressivo
- **[TypeScript](https://www.typescriptlang.org/)** - Superset tipado do JavaScript
- **[MongoDB](https://www.mongodb.com/)** - Banco de dados NoSQL
- **[Mongoose](https://mongoosejs.com/)** - ODM para MongoDB
- **[Redis](https://redis.io/)** - Cache em memória

### Pagamentos

- **[Mercado Pago SDK](https://www.mercadopago.com.br/developers)** - Integração para pagamentos PIX

### Testes

- **[Jest](https://jestjs.io/)** - Framework de testes
- **[Supertest](https://github.com/visionmedia/supertest)** - Testes de integração HTTP
- **[MongoDB Memory Server](https://github.com/nodkz/mongodb-memory-server)** - MongoDB em memória para testes

### DevOps & Qualidade

- **[Docker](https://www.docker.com/)** - Containerização
- **[SonarQube](https://www.sonarqube.org/)** - Análise de qualidade de código
- **[ESLint](https://eslint.org/)** - Linter para JavaScript/TypeScript
- **[Prettier](https://prettier.io/)** - Formatador de código

## 🏗️ Arquitetura

O projeto segue os princípios da **Arquitetura Hexagonal** (Ports & Adapters) e **Clean Architecture**:

```
src/
├── modules/
│   ├── orders/           # Módulo de pedidos
│   │   ├── controllers/  # Controladores REST
│   │   ├── control/      # Lógica de negócio
│   │   │   ├── services/ # Serviços de aplicação
│   │   │   └── repositories/ # Repositórios
│   │   ├── dto/          # Data Transfer Objects
│   │   ├── models/       # Modelos de domínio
│   │   └── schema/       # Schemas do MongoDB
│   ├── mercadopago/      # Integração Mercado Pago
│   ├── shared/           # Módulos compartilhados
│   │   ├── api/          # Filtros, interceptors
│   │   ├── database/     # Configuração do banco
│   │   └── redis/        # Configuração do Redis
│   └── cache/            # Serviços de cache
└── main.ts               # Bootstrap da aplicação
```

### Principais Padrões Utilizados

- **Dependency Injection**: Inversão de dependências com tokens
- **Repository Pattern**: Abstração da camada de dados
- **Service Layer**: Lógica de negócio isolada
- **DTO Pattern**: Validação e transferência de dados
- **Exception Handling**: Tratamento centralizado de erros

## 📋 Pré-requisitos

- **Node.js** >= 18.0.0
- **Yarn** >= 1.22.0
- **Docker** >= 20.10.0 (opcional)
- **Docker Compose** >= 2.0.0 (opcional)

## 🔧 Instalação

1. **Clone o repositório**

```bash
git clone <repository-url>
cd api-payments
```

2. **Instale as dependências**

```bash
yarn install
```

## ⚙️ Configuração

1. **Crie o arquivo de ambiente**

```bash
cp .env.example .env
```

2. **Configure as variáveis de ambiente**

```env
# Banco de dados
MONGO_URI=mongodb://localhost:27017/vitola-lanches
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password

# Cache
REDIS_URI=redis://localhost:6379

# Mercado Pago
MERCADO_PAGO_ACCESSTOKEN=your_mercado_pago_access_token

# Aplicação
PORT=3000
NODE_ENV=development
```

## 🚀 Executando a Aplicação

### Desenvolvimento Local

```bash
# Modo desenvolvimento (com hot reload)
yarn start:dev

# Modo debug
yarn start:debug

# Modo produção
yarn build
yarn start:prod
```

### Com Docker Compose

```bash
# Subir todos os serviços
docker-compose up -d

# Ou usar o script de build
./build_app.sh
```

A aplicação estará disponível em:

- **API**: http://localhost:3000
- **Documentação Swagger**: http://localhost:3000/api
- **MongoDB**: localhost:27017
- **Redis**: localhost:6379
- **SonarQube**: http://localhost:9000

## 🧪 Testes

```bash
# Executar todos os testes
yarn test

# Testes em modo watch
yarn test:watch

# Testes com coverage
yarn test:cov

# Testes end-to-end
yarn test:e2e

# Testes específicos
yarn test -- orders
yarn test -- --testNamePattern="create"
```

### Estrutura de Testes

```
test/
├── src/
│   ├── modules/          # Testes unitários por módulo
│   └── test-utils/       # Utilitários de teste
│       ├── factories/    # Factories para dados de teste
│       ├── helpers/      # Helpers para testes
│       ├── matchers/     # Matchers customizados do Jest
│       ├── mocks/        # Mocks para dependências
│       └── setup/        # Configuração global dos testes
└── app.e2e-spec.ts      # Testes end-to-end
```

## 📚 API Endpoints

### Pedidos

#### Criar Pedido

```http
POST /orders/issue
Content-Type: application/json

{
  "document": "12345678900",
  "items": [
    {
      "name": "Misto Quente",
      "description": "Delicioso misto quente",
      "quantity": 1,
      "unitPrice": 15.50
    }
  ]
}
```

#### Confirmar Pagamento

```http
PUT /orders/{orderId}/confirm
Content-Type: application/json

{
  "paymentProof": "comprovante-123",
  "totalPaid": 15.50,
  "confirmed": true
}
```

### Documentação Completa

Acesse a documentação interativa do Swagger em: `http://localhost:3000/api`

## 🐳 Docker

### Serviços Disponíveis

- **app**: Aplicação NestJS
- **mongodb**: Banco de dados MongoDB 4.4
- **redis**: Cache Redis 7.2
- **sonarqube**: Análise de qualidade de código

### Comandos Úteis

```bash
# Subir apenas o banco de dados
docker-compose up mongodb redis -d

# Ver logs da aplicação
docker-compose logs -f app

# Executar comandos dentro do container
docker-compose exec app yarn test

# Parar todos os serviços
docker-compose down

# Limpar volumes (cuidado: remove dados)
docker-compose down -v
```

## 📊 Qualidade de Código

### SonarQube

O projeto inclui configuração para análise de qualidade com SonarQube:

```bash
# Subir SonarQube
docker-compose up sonarqube -d

# Configurar projeto (primeira vez)
# 1. Acesse http://localhost:9000
# 2. Login: admin/admin
# 3. Configure o projeto conforme sonar-project.properties
```

### Linting e Formatação

```bash
# Verificar problemas de lint
yarn lint

# Corrigir problemas automaticamente
yarn lint --fix

# Formatar código
yarn format
```

### Métricas de Qualidade

- **Cobertura de Testes**: > 80%
- **Complexidade Ciclomática**: < 10
- **Duplicação de Código**: < 3%
- **Vulnerabilidades**: 0
- **Code Smells**: Minimizados

## 🔒 Segurança

### Variáveis de Ambiente

- Nunca commite arquivos `.env` com dados sensíveis
- Use `.env.example` como template
- Configure secrets adequadamente em produção

### Validação de Dados

- Todos os DTOs possuem validação
- Sanitização de inputs
- Tratamento de erros centralizado

### Autenticação (Futuro)

O projeto está preparado para implementação de autenticação JWT:

- Passport.js configurado
- Guards prontos para uso
- Estrutura para roles e permissões

## 🚀 Deploy

### Variáveis de Ambiente - Produção

```env
NODE_ENV=production
PORT=3000
MONGO_URI=mongodb://your-production-mongo-uri
REDIS_URI=redis://your-production-redis-uri
MERCADO_PAGO_ACCESSTOKEN=your-production-token
```

### Build para Produção

```bash
# Build da aplicação
yarn build

# Executar em produção
yarn start:prod
```

### Docker em Produção

```bash
# Build da imagem
docker build -t vitola-lanches-api .

# Executar container
docker run -p 3000:3000 \
  -e MONGO_URI=your-mongo-uri \
  -e REDIS_URI=your-redis-uri \
  -e MERCADO_PAGO_ACCESSTOKEN=your-token \
  vitola-lanches-api
```

## 🤝 Contribuição

### Fluxo de Desenvolvimento

1. **Fork** o projeto
2. **Crie** uma branch para sua feature (`git checkout -b feature/nova-feature`)
3. **Commit** suas mudanças (`git commit -m 'feat: adiciona nova feature'`)
4. **Push** para a branch (`git push origin feature/nova-feature`)
5. **Abra** um Pull Request

### Padrões de Commit

Seguimos o padrão [Conventional Commits](https://www.conventionalcommits.org/):

- `feat:` Nova funcionalidade
- `fix:` Correção de bug
- `docs:` Documentação
- `style:` Formatação
- `refactor:` Refatoração
- `test:` Testes
- `chore:` Tarefas de build/config

### Checklist para PRs

- [ ] Testes passando (`yarn test`)
- [ ] Lint sem erros (`yarn lint`)
- [ ] Código formatado (`yarn format`)
- [ ] Documentação atualizada
- [ ] Cobertura de testes mantida

## 📝 Licença

Este projeto está sob a licença [UNLICENSED](LICENSE).

## 👥 Equipe

- **Desenvolvedores**:
  - Elmeri Moreno

---

## 🔄 Changelog

### v0.0.1

- ✨ Implementação inicial da API
- 🔧 Configuração do ambiente de desenvolvimento
- 🧪 Setup de testes unitários e integração
- 📚 Documentação Swagger
- 🐳 Containerização com Docker
- 🔗 Integração com Mercado Pago
